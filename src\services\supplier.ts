import { z } from 'zod';
import { ServiceType, TaxRate } from '../types/project.js';
import { db } from './database.js';


// 验证模式
const createSupplierSchema = z.object({
  name: z.string().min(1, '供应商名称不能为空').max(200, '供应商名称不能超过200字符'),
  shortName: z.string().max(100, '简称不能超过100字符').optional(),
  code: z.string().max(50, '供应商编码不能超过50字符').optional(),
  contactPerson: z.string().max(100, '联系人不能超过100字符').optional(),
  contactPhone: z.string().max(20, '联系电话不能超过20字符').optional(),
  contactEmail: z.string().email('邮箱格式不正确').max(100, '邮箱不能超过100字符').optional(),
  address: z.string().optional(),
  taxNumber: z.string().max(50, '税号不能超过50字符').optional(),
  bankAccount: z.string().max(50, '银行账号不能超过50字符').optional(),
  bankName: z.string().max(200, '开户银行不能超过200字符').optional(),
  legalPerson: z.string().max(100, '法人代表不能超过100字符').optional(),
  serviceTypes: z.array(z.nativeEnum(ServiceType)).min(1, '至少选择一种服务类型'),
  preferredTaxRate: z.nativeEnum(TaxRate).optional(),
  creditLimit: z.number().min(0, '信用额度不能为负数').optional(),
  paymentTerms: z.string().optional(),
  rating: z.number().min(1).max(5, '评级必须在1-5之间').optional(),
  notes: z.string().optional(),
});


export class SupplierService {
  async createSuppliersBatch(suppliers: Array<z.infer<typeof createSupplierSchema>>, createdBy: string) {
    return await db.createSuppliersBatch(suppliers, createdBy);
  }

  async importSuppliers(file: string, createdBy: string) {
    // 解析CSV文件  
    const suppliers = this.parseCSV(file);

    // 验证数据
    const results = suppliers.map(supplier => {
      try {
        createSupplierSchema.parse(supplier);
        return { name: supplier.name, success: true, error: '' };
      } catch (error: any) {
        return { name: supplier.name, success: false, error: error.message };
      }
    });

    // 过滤成功和失败的数据
    const successfulSuppliers = results.filter(result => result.success).map(result => result.name);
    const failedSuppliers = results.filter(result => !result.success).map(result => result.name);

    const suppliersToCreate = suppliers.filter(supplier => successfulSuppliers.includes(supplier.name));

    // 创建供应商
    const createdSuppliers = await this.createSuppliersBatch(suppliersToCreate, createdBy);

    return {
      createdCount: createdSuppliers.count,
      failedCount: failedSuppliers.length,
      results
    };
  }

  private parseCSV(file: string) {
    // 达人服务 投流服务 其他服务
    // influencer advertising other
    const serverTypeMap = new Map([
      ['达人服务', 'influencer'],
      ['投流服务', 'advertising'],
      ['其他服务', 'other'],
      ['influencer', 'influencer'],
      ['advertising', 'advertising'],
      ['other', 'other']
    ]);
    // 解析CSV文件 可能是\n 或者是\r\n
    if (file.includes('\r\n')) {
      file = file.replaceAll('\r\n', '\n');
    }
    const lines: string[] = file.split('\n');
    if (lines.length === 0) {
      return [];
    }
    if (lines.length === 1) {
      return [];
    }
    if (typeof lines[0] === 'undefined') {
      return [];
    }
    const headers = lines[0].split(',');
    const data = lines.slice(1).map(line => {
      const values = line.split(',');
      return headers.reduce((obj, header, i) => {
        // 去掉空格
        header = header.trim();
        if (values[i] === undefined) {
          values[i] = '';
        }
        values[i] = values[i].trim();
        if (header === 'serviceTypes') {
          // 服务类型可能有多个，用逗号分隔
          obj[header] = values[i] === '' ? [] : values[i].split('，');
          obj[header] = obj[header].map((type: string) => serverTypeMap.get(type.trim()) || type.trim());
          return obj;
        }
        obj[header] = values[i] === '' ? undefined : values[i];
        return obj;
      }, {} as any);
    });
    if (data.length > 0) {
      return data;
    }


    return [];
  }
}
