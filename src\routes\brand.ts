import { FastifyInstance } from 'fastify';
import { BrandController } from '../controllers/brand.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function brandRoutes(fastify: FastifyInstance) {
  const brandController = new BrandController();

  
  // 品牌管理路由

  // 获取品牌列表
  fastify.get('/brands', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取品牌列表',
      tags: ['Brand'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          status: { type: 'string', enum: ['active', 'inactive'], description: '品牌状态' },
          keyword: { type: 'string', description: '品牌名称关键词' },
          sortBy: { type: 'string', enum: ['name', 'createdAt', 'updatedAt'], description: '排序字段' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], description: '排序方向' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                brands: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      description: { type: 'string' },
                      logo: { type: 'string' },
                      status: { type: 'string' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                      createdBy: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, brandController.getBrands.bind(brandController));

  // 创建品牌
  fastify.post('/brands', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BRAND_CREATE)],
    schema: {
      description: '创建品牌',
      tags: ['Brand'],
      body: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', description: '品牌名称' },
          description: { type: 'string', description: '品牌描述' },
          status: { type: 'string', enum: ['active', 'inactive'], description: '品牌状态' },
          logo: { type: 'string', description: '品牌Logo URL' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, brandController.createBrand.bind(brandController));

  // 更新品牌
  fastify.put('/brands', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BRAND_UPDATE)],
    schema: {
      description: '更新品牌',
      tags: ['Brand'],
      body: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '品牌ID' },
          name: { type: 'string', description: '品牌名称' },
          description: { type: 'string', description: '品牌描述' },
          logo: { type: 'string', description: '品牌Logo URL' },
          status: { type: 'string', enum: ['active', 'inactive'], description: '品牌状态' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, brandController.updateBrand.bind(brandController));

  // 删除品牌
  fastify.delete('/brands/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BRAND_DELETE)],
    schema: {
      description: '删除品牌',
      tags: ['Brand'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '品牌ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, brandController.deleteBrand.bind(brandController));

  // 批量添加品牌
  fastify.post('/brands/batch', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BRAND_CREATE)],
    schema: {
      description: '批量添加品牌',
      tags: ['Brand'],
      body: {
        type: 'object',
        required: ['brands'],
        properties: {
          brands: {
            type: 'array',
            items: {
              type: 'object',
              required: ['name'],
              properties: {
                name: { type: 'string', description: '品牌名称' },
                description: { type: 'string', description: '品牌描述' },
                logo: { type: 'string', description: '品牌Logo URL' },
                status: { type: 'string', enum: ['active', 'inactive'], description: '品牌状态' }
              }
            }
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                createdCount: { type: 'number' },
                failedCount: { type: 'number' },
                results: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' },
                      success: { type: 'boolean' },
                      error: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, brandController.createBrandsBatch.bind(brandController));

  console.log('🏷️ 品牌管理路由已注册');
}