import { FastifyInstance } from 'fastify';
import { ProjectController } from '../controllers/project.js';
import { ProjectImportController } from '../controllers/projectImport.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function projectRoutes(fastify: FastifyInstance) {
  const projectController = new ProjectController();
  const projectImportController = new ProjectImportController();

  // 创建项目
  fastify.post('/projects', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '创建项目',
      tags: ['Project'],
      body: {
        type: 'object',
        required: ['documentType', 'brandId', 'projectName', 'period', 'budget', 'cost', 'executorPM', 'contentMediaIds', 'contractType'],
        properties: {
          documentType: {
            type: 'string',
            enum: ['project_initiation', 'project_proposal', 'project_plan', 'project_execution', 'project_summary'],
            description: '单据类型'
          },
          brandId: {
            type: 'string',
            description: '品牌ID'
          },
          projectName: {
            type: 'string',
            description: '项目名称'
          },
          period: {
            type: 'object',
            required: ['startDate', 'endDate'],
            properties: {
              startDate: { type: 'string', format: 'date', description: '开始日期' },
              endDate: { type: 'string', format: 'date', description: '结束日期' }
            },
            description: '项目执行周期'
          },
          budget: {
            type: 'object',
            required: ['planningBudget', 'influencerBudget', 'adBudget', 'otherBudget'],
            properties: {
              planningBudget: { type: 'number', minimum: 0, description: '项目规划预算' },
              influencerBudget: { type: 'number', minimum: 0, description: '达人预算' },
              adBudget: { type: 'number', minimum: 0, description: '投流预算' },
              otherBudget: { type: 'number', minimum: 0, description: '其他预算' }
            },
            description: '项目预算'
          },
          cost: {
            type: 'object',
            required: ['influencerCost', 'adCost', 'otherCost', 'estimatedInfluencerRebate'],
            properties: {
              influencerCost: { type: 'number', minimum: 0, description: '达人成本' },
              adCost: { type: 'number', minimum: 0, description: '投流成本' },
              otherCost: { type: 'number', minimum: 0, description: '其他成本' },
              estimatedInfluencerRebate: { type: 'number', minimum: 0, description: '预估达人返点' }
            },
            description: '项目成本'
          },
          executorPM: {
            type: 'string',
            description: '执行PM用户ID'
          },
          contentMediaIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: '内容媒介用户ID列表'
          },
          contractType: {
            type: 'string',
            enum: ['annual_frame', 'quarterly_frame', 'single', 'po_order', 'jing_task'],
            description: '合同类型'
          },
          contractSigningStatus: {
            type: 'string',
            enum: ['no_contract', 'signed', 'signing', 'pending'],
            description: '合同签署情况'
          },
          settlementRules: {
            type: 'string',
            description: '项目结算规则（富文本）'
          },
          kpi: {
            type: 'string',
            description: 'KPI（富文本）'
          },
          expectedPaymentMonth: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}$',
            description: '预计回款月份 (YYYY-MM格式，如2024-03)'
          },
          paymentTermDays: {
            type: 'integer',
            minimum: 0,
            maximum: 3650,
            description: '账期天数 (如180表示T+180)'
          },
          attachmentIds: {
            type: 'array',
            items: { type: 'string' },
            description: '附件ID列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.createProject.bind(projectController));

  // 获取项目列表
  fastify.get('/projects', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取项目列表',
      tags: ['Project'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          documentType: { type: 'string', enum: ['project_initiation', 'project_proposal', 'project_plan', 'project_execution', 'project_summary'], description: '单据类型' },
          brandId: { type: 'string', description: '品牌ID' },
          contractType: { type: 'string', enum: ['annual_frame', 'quarterly_frame', 'single', 'po_order', 'jing_task'], description: '合同类型' },
          contractSigningStatus: { type: 'string', enum: ['no_contract', 'signed', 'signing', 'pending'], description: '合同签署情况' },
          executorPM: { type: 'string', description: '执行PM' },
          status: { type: 'string', enum: ['draft', 'active', 'completed', 'cancelled'], description: '项目状态' },
          keyword: { type: 'string', description: '项目名称关键词' },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          sortBy: { type: 'string', enum: ['createdAt', 'updatedAt', 'projectName', 'profit'], description: '排序字段' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], description: '排序方向' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                projects: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      documentType: { type: 'string' },
                      brandId: { type: 'string' },
                      brand: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          name: { type: 'string' }
                        }
                      },
                      period: {
                        type: 'object',
                        properties: {
                          startDate: { type: 'string' },
                          endDate: { type: 'string' }
                        }
                      },
                      budget: {
                        type: 'object',
                        properties: {
                          planningBudget: { type: 'number' },
                          influencerBudget: { type: 'number' },
                          adBudget: { type: 'number' },
                          otherBudget: { type: 'number' }
                        }
                      },
                      cost: {
                        type: 'object',
                        properties: {
                          influencerCost: { type: 'number' },
                          adCost: { type: 'number' },
                          otherCost: { type: 'number' },
                          estimatedInfluencerRebate: { type: 'number' }
                        }
                      },
                      profit: {
                        type: 'object',
                        properties: {
                          profit: { type: 'number' },
                          grossMargin: { type: 'number' }
                        }
                      },
                      executorPM: { type: 'string' }, // 执行PM用户ID
                      executorPMInfo: {
                        type: 'object',
                        properties: {
                          userid: { type: 'string' },
                          name: { type: 'string' },
                          department: { type: 'string' }
                        }
                      },
                      contentMediaIds: {
                        type: 'array',
                        items: { type: 'string' }
                      },
                      contentMediaInfo: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            userid: { type: 'string' },
                            name: { type: 'string' },
                            department: { type: 'string' }
                          }
                        }
                      },
                      contractType: { type: 'string' },
                      contractSigningStatus: { type: 'string' },
                      settlementRules: { type: 'string' },
                      kpi: { type: 'string' },
                      attachments: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            id: { type: 'string' },
                            filename: { type: 'string' },
                            originalName: { type: 'string' },
                            size: { type: 'number' },
                            mimeType: { type: 'string' },
                            url: { type: 'string' },
                            uploadedAt: { type: 'string' },
                            uploadedBy: { type: 'string' }
                          }
                        }
                      },
                      expectedPaymentMonth: { type: 'string' },
                      paymentTermDays: { type: 'number' },
                      createdBy: { type: 'string' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                      updatedBy: { type: 'string' },
                      projectName: { type: 'string' },
                      status: { type: 'string' },
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.getProjects.bind(projectController));

  // 获取单个项目
  fastify.get('/projects/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取单个项目',
      tags: ['Project'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '项目ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.getProject.bind(projectController));

  // 更新项目
  fastify.put('/projects', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '更新项目',
      tags: ['Project'],
      body: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '项目ID' },
          documentType: { type: 'string', enum: ['project_initiation', 'project_proposal', 'project_plan', 'project_execution', 'project_summary'] },
          brandId: { type: 'string' },
          projectName: { type: 'string' },
          period: {
            type: 'object',
            properties: {
              startDate: { type: 'string', format: 'date' },
              endDate: { type: 'string', format: 'date' }
            }
          },
          budget: {
            type: 'object',
            properties: {
              planningBudget: { type: 'number', minimum: 0 },
              influencerBudget: { type: 'number', minimum: 0 },
              adBudget: { type: 'number', minimum: 0 },
              otherBudget: { type: 'number', minimum: 0 }
            }
          },
          cost: {
            type: 'object',
            properties: {
              influencerCost: { type: 'number', minimum: 0 },
              adCost: { type: 'number', minimum: 0 },
              otherCost: { type: 'number', minimum: 0 },
              estimatedInfluencerRebate: { type: 'number', minimum: 0 }
            }
          },
          executorPM: { type: 'string' },
          contentMediaIds: {
            type: 'array',
            items: { type: 'string' }
          },
          contractType: {
            type: 'string',
            enum: ['annual_frame', 'quarterly_frame', 'single', 'po_order', 'jing_task']
          },
          contractSigningStatus: {
            type: 'string',
            enum: ['no_contract', 'signed', 'signing', 'pending']
          },
          settlementRules: { type: 'string' },
          kpi: { type: 'string' },
          expectedPaymentMonth: {
            type: 'string',
            pattern: '^\\d{4}-\\d{2}$',
            description: '预计回款月份 (YYYY-MM格式，如2024-03)'
          },
          paymentTermDays: {
            type: 'integer',
            minimum: 0,
            maximum: 3650,
            description: '账期天数 (如180表示T+180)'
          },
          attachmentIds: {
            type: 'array',
            items: { type: 'string' },
            description: '附件ID列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.updateProject.bind(projectController));

  // 删除项目
  fastify.delete('/projects/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PROJECT_DELETE)],
    schema: {
      description: '删除项目',
      tags: ['Project'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '项目ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.deleteProject.bind(projectController));

  // 项目文件上传
  fastify.post('/projects/upload', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '上传项目文件',
      tags: ['Project'],
      consumes: ['multipart/form-data'],
      querystring: {
        type: 'object',
        properties: {
          projectId: { type: 'string', description: '项目ID（可选，如果提供则直接关联到项目）' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                filename: { type: 'string' },
                originalName: { type: 'string' },
                size: { type: 'number' },
                mimeType: { type: 'string' },
                url: { type: 'string' },
                uploadedAt: { type: 'string' },
                uploadedBy: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.uploadProjectFile.bind(projectController));

  // 获取Excel导入模板
  fastify.get('/projects/import/template', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PROJECT_CREATE)],
    schema: {
      description: '获取Excel导入模板',
      tags: ['Project'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                headers: {
                  type: 'array',
                  items: { type: 'string' }
                },
                headerLabels: { type: 'object' },
                example: { type: 'object' },
                enums: { type: 'object' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectImportController.getImportTemplate.bind(projectImportController));

  // Excel批量导入项目
  fastify.post('/projects/import', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PROJECT_CREATE)],
    schema: {
      description: 'Excel批量导入项目',
      tags: ['Project'],
      consumes: ['multipart/form-data'],
      querystring: {
        type: 'object',
        properties: {
          createMissingBrands: { type: 'boolean', description: '创建缺失的品牌', default: true },
          defaultExecutorPM: { type: 'string', description: '默认执行PM' },
          defaultContentMediaIds: { type: 'string', description: '默认内容媒介ID（逗号分隔）' },
          dryRun: { type: 'boolean', description: '仅验证，不实际创建', default: false }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                totalRows: { type: 'number' },
                successCount: { type: 'number' },
                failureCount: { type: 'number' },
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      row: { type: 'number' },
                      field: { type: 'string' },
                      message: { type: 'string' },
                      data: { type: 'object' }
                    }
                  }
                },
                createdProjects: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      row: { type: 'number' },
                      projectId: { type: 'string' },
                      projectName: { type: 'string' },
                      revenueId: { type: 'string' }
                    }
                  }
                },
                warnings: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      row: { type: 'number' },
                      message: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectImportController.importProjects.bind(projectImportController));
}