<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV项目导入测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>CSV项目导入测试</h1>
    
    <div class="form-group">
        <label for="token">JWT Token:</label>
        <input type="text" id="token" placeholder="请输入您的JWT token">
    </div>
    
    <div class="form-group">
        <label for="csvFile">选择CSV文件:</label>
        <input type="file" id="csvFile" accept=".csv" required>
    </div>
    
    <div class="form-group">
        <label for="createMissingBrands">自动创建缺失品牌:</label>
        <select id="createMissingBrands">
            <option value="true">是</option>
            <option value="false">否</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="defaultExecutorPM">默认执行PM (可选):</label>
        <input type="text" id="defaultExecutorPM" placeholder="用户ID">
    </div>
    
    <div class="form-group">
        <label for="dryRun">试运行模式:</label>
        <select id="dryRun">
            <option value="true">是（仅验证）</option>
            <option value="false">否（实际导入）</option>
        </select>
    </div>
    
    <button onclick="testImport()">开始导入</button>
    <button onclick="getTemplate()">获取导入模板</button>
    
    <div id="result"></div>

    <script>
        const API_BASE = '/api';
        
        async function getTemplate() {
            const token = document.getElementById('token').value;
            if (!token) {
                showResult('请先输入JWT token', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/projects/import/template`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult('模板获取成功', 'success');
                    console.log('导入模板:', result.data);
                    
                    // 显示模板信息
                    const templateInfo = `
                        <h3>导入模板信息</h3>
                        <p><strong>支持的字段:</strong></p>
                        <ul>
                            ${result.data.headers.map(header => `<li>${header}: ${result.data.headerLabels[header]}</li>`).join('')}
                        </ul>
                        <p><strong>示例数据:</strong></p>
                        <pre>${JSON.stringify(result.data.example, null, 2)}</pre>
                    `;
                    document.getElementById('result').innerHTML += templateInfo;
                } else {
                    showResult(`获取模板失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testImport() {
            const token = document.getElementById('token').value;
            const fileInput = document.getElementById('csvFile');
            const createMissingBrands = document.getElementById('createMissingBrands').value;
            const defaultExecutorPM = document.getElementById('defaultExecutorPM').value;
            const dryRun = document.getElementById('dryRun').value;
            
            if (!token) {
                showResult('请先输入JWT token', 'error');
                return;
            }
            
            if (!fileInput.files[0]) {
                showResult('请选择CSV文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            // 构建查询参数
            const params = new URLSearchParams({
                createMissingBrands,
                dryRun
            });
            
            if (defaultExecutorPM) {
                params.append('defaultExecutorPM', defaultExecutorPM);
            }
            
            try {
                showResult('正在导入...', 'warning');
                
                const response = await fetch(`${API_BASE}/projects/import?${params}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    const data = result.data;
                    let message = `导入${dryRun === 'true' ? '验证' : ''}完成：成功${data.successCount}个，失败${data.failureCount}个`;
                    
                    let details = `
                        <h3>导入结果</h3>
                        <p><strong>总行数:</strong> ${data.totalRows}</p>
                        <p><strong>成功数:</strong> ${data.successCount}</p>
                        <p><strong>失败数:</strong> ${data.failureCount}</p>
                    `;
                    
                    if (data.createdProjects.length > 0) {
                        details += `
                            <h4>创建的项目:</h4>
                            <ul>
                                ${data.createdProjects.map(project => 
                                    `<li>第${project.row}行: ${project.projectName} (ID: ${project.projectId})${project.revenueId ? ` - 收入ID: ${project.revenueId}` : ''}</li>`
                                ).join('')}
                            </ul>
                        `;
                    }
                    
                    if (data.warnings.length > 0) {
                        details += `
                            <h4>警告信息:</h4>
                            <ul>
                                ${data.warnings.map(warning => `<li>第${warning.row}行: ${warning.message}</li>`).join('')}
                            </ul>
                        `;
                    }
                    
                    if (data.errors.length > 0) {
                        details += `
                            <h4>错误信息:</h4>
                            <ul>
                                ${data.errors.map(error => `<li>第${error.row}行${error.field ? ` (${error.field})` : ''}: ${error.message}</li>`).join('')}
                            </ul>
                        `;
                    }
                    
                    showResult(message, data.failureCount > 0 ? 'warning' : 'success');
                    document.getElementById('result').innerHTML += details;
                    
                } else {
                    showResult(`导入失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>
