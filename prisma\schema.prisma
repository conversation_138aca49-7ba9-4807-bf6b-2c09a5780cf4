generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 品牌库表
model Brand {
  id          String      @id @default(cuid())
  name        String      @unique @db.VarChar(100)
  description String?     @db.Text
  logo        String?     @db.VarChar(500)
  status      BrandStatus @default(ACTIVE)

  // 关联项目
  projects Project[]

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdBy String   @db.VarChar(50)

  @@index([status])
  @@index([name])
  @@map("brands")
}

// 项目主表
model Project {
  id String @id @default(cuid())

  // 基本信息
  documentType DocumentType @default(PROJECT_INITIATION)
  brandId      String
  projectName  String       @db.VarChar(200)

  // 项目执行周期
  startDate DateTime @db.Date
  endDate   DateTime @db.Date

  // 预算信息 (使用Decimal确保金额精度)
  planningBudget   Decimal @db.Decimal(15, 2) // 项目规划预算
  influencerBudget Decimal @db.Decimal(15, 2) // 达人预算
  adBudget         Decimal @db.Decimal(15, 2) // 投流预算
  otherBudget      Decimal @db.Decimal(15, 2) // 其他预算

  // 财务回款信息
  expectedPaymentMonth String? @db.VarChar(7) // 预计回款月份 (YYYY-MM格式，如2024-03)
  paymentTermDays      Int?    @db.SmallInt // 账期天数 (如180表示T+180)

  // 成本信息
  influencerCost            Decimal @db.Decimal(15, 2) // 达人成本
  adCost                    Decimal @db.Decimal(15, 2) // 投流成本
  otherCost                 Decimal @db.Decimal(15, 2) // 其他成本
  estimatedInfluencerRebate Decimal @db.Decimal(15, 2) // 预估达人返点

  // 计算字段 (通过数据库视图或应用层计算)
  // 项目利润 = 项目规划预算 - (达人成本 + 投流成本 + 其他成本) + 预估达人返点
  // 项目毛利 = 项目利润 / 项目规划预算

  // 人员信息
  executorPM      String   @db.VarChar(50) // 执行PM (钉钉用户ID)
  contentMediaIds String[] // 内容媒介 (钉钉用户ID数组)

  // 合同信息
  contractType          ContractType // 合同类型
  contractSigningStatus ContractSigningStatus @default(PENDING) // 合同签署情况
  settlementRules       String                @db.Text // 项目结算规则 (富文本)
  kpi                   String                @db.Text // KPI (富文本)

  // 项目状态
  status ProjectStatus @default(DRAFT)

  // 关联关系
  brand         Brand              @relation(fields: [brandId], references: [id], onDelete: Restrict)
  attachments   Attachment[] // 项目附件
  revenues      ProjectRevenue[] // 项目预计收入
  weeklyBudgets WeeklyBudget[] // 项目周预算
  changeLogs    ProjectChangeLog[] // 项目变更记录
  metadata      Json? // 项目元数据 (JSON格式存储自定义字段)

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdBy String   @db.VarChar(50)
  updatedBy String   @db.VarChar(50)

  @@index([brandId, status])
  @@index([executorPM])
  @@index([contractType])
  @@index([contractSigningStatus])
  @@index([startDate, endDate])
  @@index([createdAt])
  @@index([status])
  @@index([expectedPaymentMonth])
  @@index([paymentTermDays])
  @@map("projects")
}

// 项目附件表
model Attachment {
  id           String @id @default(cuid())
  filename     String @db.VarChar(255) // 存储文件名
  originalName String @db.VarChar(255) // 原始文件名
  size         BigInt // 文件大小 (字节)
  mimeType     String @db.VarChar(100) // MIME类型
  url          String @db.VarChar(500) // 文件访问URL

  // 关联项目
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 上传信息
  uploadedBy String   @db.VarChar(50) // 上传者 (钉钉用户ID)
  uploadedAt DateTime @default(now()) @db.Timestamptz

  @@index([projectId])
  @@index([uploadedBy])
  @@map("attachments")
}

// 项目变更记录表
model ProjectChangeLog {
  id String @id @default(cuid())

  // 基本信息
  changeType    ChangeType @default(UPDATE) // 变更类型
  changeTitle   String     @db.VarChar(200) // 变更标题
  changeDetails Json? // 变更详情 (JSON格式存储具体变更内容)

  // 变更前后数据对比
  beforeData Json? // 变更前的数据
  afterData  Json? // 变更后的数据

  // 变更字段信息
  changedFields String[] // 变更的字段列表

  // 操作信息
  operatorId   String  @db.VarChar(50) // 操作人员钉钉用户ID
  operatorName String  @db.VarChar(100) // 操作人员姓名 (冗余字段，便于查询)
  operatorIP   String? @db.VarChar(45) // 操作人员IP地址
  userAgent    String? @db.VarChar(500) // 用户代理信息

  // 业务信息
  reason      String? @db.Text // 变更原因
  description String? @db.Text // 变更描述

  // 关联项目
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 时间信息
  createdAt DateTime @default(now()) @db.Timestamptz

  @@index([projectId, createdAt])
  @@index([operatorId])
  @@index([changeType])
  @@index([createdAt])
  @@map("project_change_logs")
}

// 项目预计收入表
model ProjectRevenue {
  id String @id @default(cuid())

  // 基本信息
  title       String        @db.VarChar(200) // 收入标题/描述
  revenueType RevenueType   @default(PROJECT_INCOME) // 收入类型
  status      RevenueStatus @default(RECEIVING) // 收入状态

  // 金额信息
  plannedAmount Decimal  @db.Decimal(15, 2) // 预计收入金额
  actualAmount  Decimal? @db.Decimal(15, 2) // 实际收入金额
  invoiceAmount Decimal? @db.Decimal(15, 2) // 开票金额

  // 时间信息
  plannedDate   DateTime? @db.Date // 预计收入时间
  confirmedDate DateTime? @db.Date // 确认收入时间
  invoiceDate   DateTime? @db.Date // 开票时间
  receivedDate  DateTime? @db.Date // 实际收款时间

  // 业务信息
  milestone     String? @db.VarChar(200) // 里程碑描述
  invoiceNumber String? @db.VarChar(100) // 发票号码
  paymentTerms  String? @db.Text // 付款条件
  notes         String? @db.Text // 备注说明

  // 关联项目
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdBy String   @db.VarChar(50) // 创建者 (钉钉用户ID)
  updatedBy String   @db.VarChar(50) // 更新者 (钉钉用户ID)

  @@index([projectId, status])
  @@index([plannedDate])
  @@index([status])
  @@index([revenueType])
  @@index([createdAt])
  @@map("project_revenues")
}

// 项目周预算表
model WeeklyBudget {
  id String @id @default(cuid())

  // 基本信息
  title         String   @db.VarChar(200) // 预算标题
  weekStartDate DateTime @db.Date // 周开始日期
  weekEndDate   DateTime @db.Date // 周结束日期
  weekNumber    Int      @db.SmallInt // 第几周
  year          Int      @db.SmallInt // 年份

  // 服务信息
  serviceType    ServiceType // 服务类型
  serviceContent String      @db.Text // 服务内容描述
  remarks        String?     @db.Text // 备注

  // 财务信息
  contractAmount  Decimal @db.Decimal(15, 2) // 合同金额
  taxRate         TaxRate // 税率
  paidAmount      Decimal @default(0) @db.Decimal(15, 2) // 已付金额
  remainingAmount Decimal @db.Decimal(15, 2) // 剩余金额 (计算字段)

  // 状态信息
  status WeeklyBudgetStatus @default(CREATED) // 预算状态

  // 审批信息
  approvalStatus ApprovalStatus @default(NONE) // 审批状态
  approvalAmount Decimal?       @db.Decimal(15, 2) // 审批金额
  approvalReason String?        @db.Text // 审批原因/说明

  // 关联关系
  projectId String // 关联项目
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  supplierId String? // 关联供应商
  supplier   Supplier? @relation(fields: [supplierId], references: [id], onDelete: SetNull)

  // 审批实例关联
  approvalInstances ApprovalInstance[] // 审批实例

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdBy String   @db.VarChar(50) // 创建者 (钉钉用户ID)
  updatedBy String   @db.VarChar(50) // 更新者 (钉钉用户ID)

  @@index([projectId, weekStartDate])
  @@index([supplierId])
  @@index([serviceType])
  @@index([status])
  @@index([approvalStatus])
  @@index([year, weekNumber])
  @@index([createdAt])
  @@map("weekly_budgets")
}

// 供应商管理表
model Supplier {
  id String @id @default(cuid())

  // 基本信息
  name      String  @db.VarChar(200) // 供应商名称
  shortName String? @db.VarChar(100) // 简称
  code      String? @unique @db.VarChar(50) // 供应商编码

  // 联系信息
  contactPerson String? @db.VarChar(100) // 联系人
  contactPhone  String? @db.VarChar(20) // 联系电话
  contactEmail  String? @db.VarChar(100) // 联系邮箱
  address       String? @db.Text // 地址

  // 企业信息
  taxNumber   String? @db.VarChar(50) // 税号
  bankAccount String? @db.VarChar(50) // 银行账号
  bankName    String? @db.VarChar(200) // 开户银行
  legalPerson String? @db.VarChar(100) // 法人代表

  // 业务信息
  serviceTypes     ServiceType[] // 服务类型
  preferredTaxRate TaxRate? // 首选税率
  creditLimit      Decimal?      @db.Decimal(15, 2) // 信用额度
  paymentTerms     String?       @db.Text // 付款条件

  // 状态和评级
  status SupplierStatus @default(ACTIVE) // 供应商状态
  rating Int?           @db.SmallInt // 评级 (1-5)
  notes  String?        @db.Text // 备注

  // 关联关系
  weeklyBudgets WeeklyBudget[] // 周预算

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdBy String   @db.VarChar(50) // 创建者 (钉钉用户ID)
  updatedBy String   @db.VarChar(50) // 更新者 (钉钉用户ID)

  @@index([name])
  @@index([status])
  @@index([serviceTypes])
  @@index([createdAt])
  @@map("suppliers")
}

// 用户信息缓存表 (缓存钉钉用户信息)
model User {
  userid               String    @id @db.VarChar(50) // 钉钉用户ID
  unionid              String?   @db.VarChar(100) // 员工在当前开发者企业账号范围内的唯一标识
  name                 String    @db.VarChar(100) // 用户姓名
  avatar               String?   @db.VarChar(500) // 头像URL
  stateCode            String?   @db.VarChar(10) // 国际电话区号
  managerUserid        String?   @db.VarChar(50) // 直属主管userid
  mobile               String?   @db.VarChar(20) // 手机号
  hideMobile           Boolean?  @default(false) // 是否隐藏手机号
  telephone            String?   @db.VarChar(20) // 分机号
  jobNumber            String?   @db.VarChar(50) // 工号
  title                String?   @db.VarChar(100) // 职位
  email                String?   @db.VarChar(100) // 邮箱
  workPlace            String?   @db.VarChar(200) // 办公地点
  remark               String?   @db.Text // 备注
  loginId              String?   @db.VarChar(100) // 专属帐号登录名
  exclusiveAccountType String?   @db.VarChar(20) // 专属帐号类型
  exclusiveAccount     Boolean?  @default(false) // 是否专属帐号
  deptIdList           Int[] // 所属部门ID列表
  extension            String?   @db.Text // 扩展属性
  hiredDate            DateTime? @db.Timestamptz // 入职时间
  active               Boolean?  @default(true) // 是否激活了钉钉
  realAuthed           Boolean?  @default(false) // 是否完成了实名认证
  orgEmail             String?   @db.VarChar(100) // 企业邮箱
  orgEmailType         String?   @db.VarChar(50) // 企业邮箱类型
  senior               Boolean?  @default(false) // 是否为企业的高管
  admin                Boolean?  @default(false) // 是否为企业的管理员
  boss                 Boolean?  @default(false) // 是否为企业的老板

  // 同步信息
  lastSyncAt DateTime @default(now()) @db.Timestamptz
  isActive   Boolean  @default(true) // 是否在系统中激活

  // 权限管理关联
  userRoles UserRole[]

  @@index([name])
  @@index([isActive])
  @@index([mobile])
  @@index([email])
  @@index([managerUserid])
  @@map("users")
}

// 部门信息缓存表 (缓存钉钉部门信息)
model Department {
  deptId                Int      @id // 钉钉部门ID
  name                  String   @db.VarChar(200) // 部门名称
  parentId              Int      @default(1) // 父部门ID
  createDeptGroup       Boolean  @default(false) // 是否同时创建企业群
  autoAddUser           Boolean  @default(false) // 当群满员后，是否自动加群
  fromUnionOrg          Boolean  @default(false) // 是否来自关联组织
  tags                  String?  @db.VarChar(500) // 部门标签
  order                 Int      @default(0) // 在父部门中的次序值
  deptManagerUseridList String[] // 部门主管用户ID列表
  outerDept             Boolean  @default(false) // 是否限制本部门成员查看通讯录
  outerPermitDepts      Int[] // 配置的部门员工可见部门列表
  outerPermitUsers      String[] // 配置的部门员工可见员工列表
  orgDeptOwner          String?  @db.VarChar(50) // 部门的主管
  deptPerimits          Int      @default(0) // 企业群中@all权限
  userPerimits          Int      @default(0) // 企业群中群成员查看成员列表的权限
  outerDeptOnlySelf     Boolean  @default(false) // 只能看到所在部门及下级部门通讯录
  sourceIdentifier      String?  @db.VarChar(100) // 部门标识字段
  ext                   String?  @db.Text // 扩展字段
  hideSceneConfig       Json? // 隐藏场景配置

  // 同步信息
  lastSyncAt DateTime @default(now()) @db.Timestamptz

  // 权限管理关联
  departmentRoles DepartmentRole[]

  @@index([name])
  @@index([parentId])
  @@index([order])
  @@index([lastSyncAt])
  @@map("departments")
}

// 角色表
model Role {
  id          String  @id @default(cuid())
  name        String  @unique @db.VarChar(100) // 角色名称
  displayName String  @db.VarChar(100) // 角色显示名称
  description String? @db.Text // 角色描述
  isSystem    Boolean @default(false) // 是否为系统内置角色
  isActive    Boolean @default(true) // 是否激活

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdBy String   @db.VarChar(50) // 创建者用户ID

  // 关联关系
  rolePermissions RolePermission[]
  userRoles       UserRole[]
  departmentRoles DepartmentRole[]

  @@index([name])
  @@index([isActive])
  @@index([isSystem])
  @@map("roles")
}

// 权限表
model Permission {
  id          String  @id @default(cuid())
  name        String  @unique @db.VarChar(100) // 权限名称 (如: project.create, user.read)
  displayName String  @db.VarChar(100) // 权限显示名称
  description String? @db.Text // 权限描述
  module      String  @db.VarChar(50) // 所属模块 (如: project, user, brand)
  action      String  @db.VarChar(50) // 操作类型 (如: create, read, update, delete)
  resource    String? @db.VarChar(50) // 资源类型 (可选，用于细粒度控制)
  isSystem    Boolean @default(false) // 是否为系统内置权限

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // 关联关系
  rolePermissions RolePermission[]

  @@index([module])
  @@index([action])
  @@index([name])
  @@map("permissions")
}

// 角色权限关联表
model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  createdBy String   @db.VarChar(50) // 分配者用户ID

  // 关联关系
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
  @@map("role_permissions")
}

// 用户角色关联表 (直接分配给用户的角色)
model UserRole {
  id     String @id @default(cuid())
  userid String @db.VarChar(50) // 钉钉用户ID
  roleId String

  // 审计字段
  createdAt DateTime  @default(now()) @db.Timestamptz
  createdBy String    @db.VarChar(50) // 分配者用户ID
  expiresAt DateTime? @db.Timestamptz // 角色过期时间 (可选)

  // 关联关系
  user User @relation(fields: [userid], references: [userid], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userid, roleId])
  @@index([userid])
  @@index([roleId])
  @@index([expiresAt])
  @@map("user_roles")
}

// 部门角色关联表 (分配给部门的角色，部门成员自动继承)
model DepartmentRole {
  id     String @id @default(cuid())
  deptId Int // 钉钉部门ID
  roleId String

  // 审计字段
  createdAt DateTime  @default(now()) @db.Timestamptz
  createdBy String    @db.VarChar(50) // 分配者用户ID
  expiresAt DateTime? @db.Timestamptz // 角色过期时间 (可选)

  // 关联关系
  department Department @relation(fields: [deptId], references: [deptId], onDelete: Cascade)
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([deptId, roleId])
  @@index([deptId])
  @@index([roleId])
  @@index([expiresAt])
  @@map("department_roles")
}

// 审批实例表
model ApprovalInstance {
  id String @id @default(cuid())

  // 钉钉审批信息
  processInstanceId String  @unique @db.VarChar(100) // 钉钉审批实例ID
  processCode       String  @db.VarChar(100) // 审批模板代码
  businessId        String? @db.VarChar(100) // 业务ID

  // 审批基本信息
  title            String         @db.VarChar(200) // 审批标题
  originatorUserId String         @db.VarChar(50) // 发起人钉钉用户ID
  status           ApprovalStatus @default(PENDING) // 审批状态
  result           String?        @db.VarChar(50) // 审批结果 (agree/refuse)

  // 审批时间
  createTime DateTime  @db.Timestamptz // 创建时间
  finishTime DateTime? @db.Timestamptz // 完成时间

  // 审批金额信息
  approvalAmount Decimal  @db.Decimal(15, 2) // 审批金额
  actualAmount   Decimal? @db.Decimal(15, 2) // 实际金额

  // 审批说明
  reason String? @db.Text // 审批原因
  remark String? @db.Text // 备注

  // 关联关系
  weeklyBudgetId String // 关联周预算
  weeklyBudget   WeeklyBudget @relation(fields: [weeklyBudgetId], references: [id], onDelete: Cascade)

  // 审计字段
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  @@index([processInstanceId])
  @@index([weeklyBudgetId])
  @@index([status])
  @@index([originatorUserId])
  @@index([createTime])
  @@map("approval_instances")
}

// 枚举定义
enum BrandStatus {
  ACTIVE // 启用
  INACTIVE // 禁用

  @@map("brand_status")
}

enum DocumentType {
  PROJECT_INITIATION // 项目立项表
  PROJECT_PROPOSAL // 项目提案
  PROJECT_PLAN // 项目计划
  PROJECT_EXECUTION // 项目执行
  PROJECT_SUMMARY // 项目总结

  @@map("document_type")
}

enum ContractType {
  ANNUAL_FRAME // 年框
  QUARTERLY_FRAME // 季框
  SINGLE // 单次
  PO_ORDER // PO单
  JING_TASK // 京任务

  @@map("contract_type")
}

enum ProjectStatus {
  DRAFT // 草稿
  ACTIVE // 进行中
  COMPLETED // 已完成
  CANCELLED // 已取消

  @@map("project_status")
}

enum RevenueStatus {
  RECEIVING // 收款中
  RECEIVED // 已收款
  CANCELLED // 已取消

  @@map("revenue_status")
}

enum RevenueType {
  INFLUENCER_INCOME // 达人收入
  PROJECT_INCOME // 项目收入
  OTHER // 其他收入

  @@map("revenue_type")
}

enum SupplierStatus {
  ACTIVE // 活跃
  INACTIVE // 停用
  PENDING // 待审核
  BLACKLISTED // 黑名单

  @@map("supplier_status")
}

enum ServiceType {
  INFLUENCER // 达人服务
  ADVERTISING // 投流服务
  OTHER // 其他服务

  @@map("service_type")
}

enum TaxRate {
  SPECIAL_1 // 专票1%
  SPECIAL_3 // 专票3%
  SPECIAL_6 // 专票6%
  GENERAL // 普票

  @@map("tax_rate")
}

enum WeeklyBudgetStatus {
  CREATED // 草稿
  APPROVED // 已批准
  EXECUTING // 执行中
  COMPLETED // 已完成
  CANCELLED // 已取消

  @@map("weekly_budget_status")
}

enum ApprovalStatus {
  NONE // 无审批
  PENDING // 审批中
  APPROVED // 已通过
  REJECTED // 已拒绝
  CANCELLED // 已撤销

  @@map("approval_status")
}

enum ContractSigningStatus {
  NO_CONTRACT // 无合同
  SIGNED // 已签订
  SIGNING // 签订中
  PENDING // 待定

  @@map("contract_signing_status")
}

enum ChangeType {
  CREATE // 创建
  UPDATE // 更新
  DELETE // 删除
  STATUS_CHANGE // 状态变更
  APPROVAL // 审批操作
  ATTACHMENT // 附件操作

  @@map("change_type")
}
