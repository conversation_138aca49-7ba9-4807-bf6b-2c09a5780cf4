import { FastifyInstance } from 'fastify';
import { RevenueController } from '../controllers/revenue.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function revenueRoutes(fastify: FastifyInstance) {
  const revenueController = new RevenueController();

  // 项目收入管理路由

  // 创建项目收入
  fastify.post('/projects/:projectId/revenues', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_CREATE)],
    schema: {
      description: '创建项目收入',
      tags: ['Revenue'],
      params: {
        type: 'object',
        required: ['projectId'],
        properties: {
          projectId: { type: 'string', description: '项目ID' }
        }
      },
      body: {
        type: 'object',
        required: ['title', 'revenueType', 'plannedAmount'],
        properties: {
          title: { type: 'string', description: '收入标题' },
          revenueType: {
            type: 'string',
            enum: ['influencer_income', 'project_income', 'other'],
            description: '收入类型'
          },
          plannedAmount: { type: 'number', description: '预计收入金额' },
          plannedDate: { type: 'string', format: 'date', description: '预计收入时间' },
          milestone: { type: 'string', description: '里程碑描述' },
          paymentTerms: { type: 'string', description: '付款条件' },
          notes: { type: 'string', description: '备注说明' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.createRevenue.bind(revenueController));

  // 获取项目收入列表
  fastify.get('/revenues', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_READ)],
    schema: {
      description: '获取项目收入列表',
      tags: ['Revenue'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          projectId: { type: 'string', description: '项目ID' },
          status: {
            type: 'string',
            enum: ['receiving', 'received', 'cancelled'],
            description: '收入状态'
          },
          revenueType: {
            type: 'string',
            enum: ['influencer_income', 'project_income', 'other'],
            description: '收入类型'
          },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          sortBy: {
            type: 'string',
            enum: ['actualAmount', 'createdAt'],
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            description: '排序方向'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                revenues: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      revenueType: { type: 'string' },
                      status: { type: 'string' },
                      plannedAmount: { type: 'number' },
                      actualAmount: { type: 'number' },
                      invoiceAmount: { type: 'number' },
                      plannedDate: { type: 'string' },
                      confirmedDate: { type: 'string' },
                      invoiceDate: { type: 'string' },
                      receivedDate: { type: 'string' },
                      milestone: { type: 'string' },
                      invoiceNumber: { type: 'string' },
                      paymentTerms: { type: 'string' },
                      notes: { type: 'string' },
                      projectId: { type: 'string' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                      createdBy: { type: 'string' },
                      updatedBy: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, revenueController.getRevenues.bind(revenueController));

  // 获取单个项目收入
  fastify.get('/revenues/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_READ)],
    schema: {
      description: '获取单个项目收入',
      tags: ['Revenue'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '收入ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.getRevenue.bind(revenueController));

  // 更新项目收入
  fastify.put('/revenues/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_UPDATE)],
    schema: {
      description: '更新项目收入',
      tags: ['Revenue'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '收入ID' }
        }
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', description: '收入标题' },
          revenueType: {
            type: 'string',
            enum: ['influencer_income', 'project_income', 'other'],
            description: '收入类型'
          },
          status: {
            type: 'string',
            enum: ['receiving', 'received', 'cancelled'],
            description: '收入状态'
          },
          plannedAmount: { type: 'number', description: '预计收入金额' },
          actualAmount: { type: 'number', description: '实际收入金额' },
          invoiceAmount: { type: 'number', description: '开票金额' },
          plannedDate: { type: 'string', format: 'date', description: '预计收入时间' },
          confirmedDate: { type: 'string', format: 'date', description: '确认收入时间' },
          invoiceDate: { type: 'string', format: 'date', description: '开票时间' },
          receivedDate: { type: 'string', format: 'date', description: '实际收款时间' },
          milestone: { type: 'string', description: '里程碑描述' },
          invoiceNumber: { type: 'string', description: '发票号码' },
          paymentTerms: { type: 'string', description: '付款条件' },
          notes: { type: 'string', description: '备注说明' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.updateRevenue.bind(revenueController));

  // 删除项目收入
  fastify.delete('/revenues/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_DELETE)],
    schema: {
      description: '删除项目收入',
      tags: ['Revenue'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '收入ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.deleteRevenue.bind(revenueController));

  // 确认收入
  fastify.put('/revenues/:id/confirm', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_APPROVE)],
    schema: {
      description: '确认收入',
      tags: ['Revenue'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '收入ID' }
        }
      },
      body: {
        type: 'object',
        required: ['actualAmount'],
        properties: {
          actualAmount: { type: 'number', description: '实际收入金额', minimum: 0 },
          confirmedDate: { type: 'string', format: 'date', description: '确认收入时间' },
          notes: { type: 'string', description: '确认备注' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.confirmRevenue.bind(revenueController));

  // 批量确认收入
  fastify.put('/revenues/batch-confirm', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_APPROVE)],
    schema: {
      description: '批量确认收入',
      tags: ['Revenue'],
      body: {
        type: 'object',
        required: ['revenues'],
        properties: {
          revenues: {
            type: 'array',
            items: {
              type: 'object',
              required: ['id', 'actualAmount'],
              properties: {
                id: { type: 'string', description: '收入ID' },
                actualAmount: { type: 'number', description: '实际收入金额', minimum: 0 },
                confirmedDate: { type: 'string', format: 'date', description: '确认收入时间' },
                notes: { type: 'string', description: '确认备注' }
              }
            },
            minItems: 1,
            maxItems: 100,
            description: '要确认的收入列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                successCount: { type: 'number', description: '成功确认数量' },
                failureCount: { type: 'number', description: '失败确认数量' },
                results: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      success: { type: 'boolean' },
                      data: { type: 'object' },
                      error: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.batchConfirmRevenues.bind(revenueController));

  // 获取收入统计
  fastify.get('/revenues/stats', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_READ)],
    schema: {
      description: '获取收入统计',
      tags: ['Revenue'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, revenueController.getRevenueStats.bind(revenueController));

  console.log('📊 收入管理路由已注册');
}
