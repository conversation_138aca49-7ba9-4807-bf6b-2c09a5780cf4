# 权限中间件迁移测试

## 修改总结

已成功将 `adminAuthMiddleware` 替换为基于权限的中间件：

### 修改的路由

1. **删除项目路由** (`DELETE /projects/:id`)
   - **修改前**: `preHandler: [jwtAuthMiddleware, adminAuthMiddleware]`
   - **修改后**: `preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PROJECT_DELETE)]`
   - **权限要求**: `project.delete`

2. **创建品牌路由** (`POST /brands`)
   - **修改前**: `preHandler: [jwtAuthMiddleware, adminAuthMiddleware]`
   - **修改后**: `preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BRAND_CREATE)]`
   - **权限要求**: `brand.create`

### 代码变更

#### src/routes/project.ts
```typescript
// 导入变更
- import { adminAuthMiddleware, jwtAuthMiddleware } from '../middleware/auth.js';
+ import { jwtAuthMiddleware } from '../middleware/auth.js';
+ import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

// 删除项目路由
- preHandler: [jwtAuthMiddleware, adminAuthMiddleware],
+ preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PROJECT_DELETE)],

// 创建品牌路由  
- preHandler: [jwtAuthMiddleware, adminAuthMiddleware],
+ preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BRAND_CREATE)],
```

## 权限验证逻辑

### 新的权限验证流程

1. **用户认证**: `jwtAuthMiddleware` 验证用户身份
2. **权限检查**: `requirePermission()` 检查用户是否具有特定权限
3. **管理员绕过**: 管理员和老板默认可以绕过权限检查（可配置）
4. **权限继承**: 用户权限 = 直接分配的角色权限 + 部门角色权限

### 权限分配

根据系统默认角色配置：

- **super_admin**: 拥有所有权限
- **admin**: 拥有 `project.delete` 和 `brand.create` 权限
- **project_manager**: 拥有 `project.create`, `project.update` 但没有 `project.delete`
- **user**: 只有基本查看权限

## 测试建议

### 1. 功能测试
```bash
# 启动应用
npm run dev

# 测试删除项目权限
curl -X DELETE http://localhost:3000/api/projects/test-id \
  -H "Authorization: Bearer <jwt-token>"

# 测试创建品牌权限  
curl -X POST http://localhost:3000/api/brands \
  -H "Authorization: Bearer <jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "测试品牌"}'
```

### 2. 权限测试场景

1. **管理员用户**: 应该能够访问所有功能
2. **项目经理**: 应该无法删除项目，无法创建品牌
3. **普通用户**: 应该无法删除项目，无法创建品牌
4. **未分配权限的用户**: 应该收到 403 权限不足错误

### 3. 错误响应格式

权限不足时的响应：
```json
{
  "success": false,
  "message": "权限不足",
  "code": "INSUFFICIENT_PERMISSIONS",
  "data": {
    "requiredPermissions": ["project.delete"],
    "missingPermissions": ["project.delete"],
    "requireAll": false
  }
}
```

## 优势

1. **细粒度控制**: 可以精确控制每个功能的访问权限
2. **灵活配置**: 通过角色和权限分配，可以灵活调整用户权限
3. **审计友好**: 权限检查失败时提供详细的错误信息
4. **扩展性好**: 新增功能时只需定义相应权限即可
5. **部门继承**: 支持部门级别的权限分配和继承

## 注意事项

1. 确保权限系统已正确初始化
2. 新用户需要分配适当的角色才能访问功能
3. 权限变更后可能需要用户重新登录
4. 管理员和老板默认绕过权限检查，如需限制可配置 `allowAdmin: false`
