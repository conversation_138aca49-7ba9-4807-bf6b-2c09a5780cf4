import { FastifyReply, FastifyRequest } from "fastify";
import fs from "fs";
import path, { dirname } from "path";
import { pipeline } from "stream";
import { fileURLToPath } from 'url';
import { promisify } from "util";
import { z } from "zod";
import { DingTalkService } from "../services/dingtalk.js";

const pump = promisify(pipeline);
// 在 ESM 环境下手动构造 __dirname
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 请求参数验证模式
const departmentUsersSchema = z.object({
  deptId: z.number().min(1, "部门ID必须大于0").optional(), // 改为可选参数
  cursor: z.number().optional(),
  size: z.number().max(100, "每页最多100条记录").optional(),
});

const sendMessageSchema = z.object({
  userIds: z.array(z.string()).min(1, "用户ID列表不能为空"),
  title: z.string().min(1, "标题不能为空"),
  content: z.string().min(1, "内容不能为空"),
  messageType: z
    .enum(["text", "markdown", "actionCard"])
    .optional()
    .default("text"),
});

export class AppController {
  private dingTalkService: DingTalkService;

  constructor() {
    this.dingTalkService = new DingTalkService();
  }

  /**
   * 获取应用配置信息
   */
  async getAppConfig(request: FastifyRequest, reply: FastifyReply) {
    try {
      const config = this.dingTalkService.getAppConfig();

      return reply.send({
        success: true,
        data: config,
        message: "获取应用配置成功",
      });
    } catch (error) {
      console.error("获取应用配置失败:", error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : "获取应用配置失败",
      });
    }
  }

  /**
   * 获取部门用户列表或所有用户
   */
  async getDepartmentUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const parsed = departmentUsersSchema.parse(request.query);
      const deptId = parsed.deptId;
      const cursor = parsed.cursor || 0;
      const size = parsed.size || 100;

      let users;
      let message;

      if (deptId) {
        // 获取指定部门的用户
        users = await this.dingTalkService.getDepartmentUsers(
          deptId,
          cursor,
          size
        );
        message = "获取部门用户列表成功";
      } else {
        // 获取所有用户
        users = await this.dingTalkService.getAllUsers(cursor, size);
        message = "获取所有用户列表成功";
      }

      return reply.send({
        success: true,
        data: users,
        message,
      });
    } catch (error) {
      console.error("获取用户列表失败:", error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: "参数验证失败",
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : "获取用户列表失败",
      });
    }
  }

  /**
   * 发送工作通知
   */
  async sendWorkNotification(request: FastifyRequest, reply: FastifyReply) {
    try {
      const messageData = sendMessageSchema.parse(request.body);

      const result = await this.dingTalkService.sendWorkNotification(
        messageData
      );

      return reply.send({
        success: true,
        data: { sent: result },
        message: "发送工作通知成功",
      });
    } catch (error) {
      console.error("发送工作通知失败:", error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: "参数验证失败",
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : "发送工作通知失败",
      });
    }
  }

  /**
   * 获取增强版JSAPI签名
   */
  async getEnhancedJSAPISignature(
    request: FastifyRequest,
    reply: FastifyReply
  ) {
    try {
      const { url } = z
        .object({
          url: z.string().url("URL格式不正确"),
        })
        .parse(request.query);

      const signature =
        await this.dingTalkService.generateCorrectJSAPISignature(url);

      return reply.send({
        success: true,
        data: signature,
        message: "获取增强版JSAPI签名成功",
      });
    } catch (error) {
      console.error("获取增强版JSAPI签名失败:", error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: "参数验证失败",
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message:
          error instanceof Error ? error.message : "获取增强版JSAPI签名失败",
      });
    }
  }

  /**
   * 上传文件
   */
  async uploadFile(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 1) 从请求中读取第一个 file 字段
      const data = await request.file();
      if (!data || data.fieldname !== "file") {
        return reply.status(400).send({
          success: false,
          message: "未找到名为 file 的上传字段",
        });
      }
      const { file, filename } = data;

      // 2) 确保上传目录存在
      const uploadDir = path.resolve(__dirname, "../../uploads");
      await fs.promises.mkdir(uploadDir, { recursive: true });

      // 3) 写入磁盘
      const target = path.join(uploadDir, filename);
      await pump(file, fs.createWriteStream(target));

      // 4) 返回 JSON
      return reply.status(200).send({
        success: true,
        message: "上传成功",
        filename,
      });
    } catch (err) {
      request.log.error(err, "文件上传失败");
      return reply.status(500).send({
        success: false,
        message: err instanceof Error ? err.message : "文件上传失败",
      });
    }
  }

  /**
   * 获取当前用户权限
   */
  async getUserPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 这里可以根据用户ID获取权限信息
      // 目前返回模拟数据
      const permissions = {
        userid: "current_user",
        permissions: [
          "read:user",
          "read:department",
          "send:notification",
          "upload:file",
        ],
        roles: ["employee", "user"],
      };

      return reply.send({
        success: true,
        data: permissions,
        message: "获取用户权限成功",
      });
    } catch (error) {
      console.error("获取用户权限失败:", error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : "获取用户权限失败",
      });
    }
  }

  /**
   * 获取应用统计信息
   */
  async getAppStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = {
        totalUsers: 0,
        totalDepartments: 0,
        onlineUsers: 0,
        messagesSent: 0,
        lastUpdate: new Date().toISOString(),
      };

      return reply.send({
        success: true,
        data: stats,
        message: "获取应用统计信息成功",
      });
    } catch (error) {
      console.error("获取应用统计信息失败:", error);

      return reply.status(500).send({
        success: false,
        message:
          error instanceof Error ? error.message : "获取应用统计信息失败",
      });
    }
  }

  // 获取钉盘spaceid
  async getDingTalkSpaceId(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const spaceId = await this.dingTalkService.getDingTalkSpaceId(user.userid);

      return reply.send({
        success: true,
        data: {spaceId},
        message: "获取钉盘spaceid成功",
      });
    } catch (error) {
      console.error("获取钉盘spaceid失败:", error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : "获取钉盘spaceid失败",
      });
    }
  }
}
