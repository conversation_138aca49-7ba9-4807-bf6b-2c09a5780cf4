import { FastifyInstance } from 'fastify';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';
import { DatabaseService } from '../services/database.js';
import { DepartmentSyncService } from '../services/departmentSync.js';
import { DingTalkService } from '../services/dingtalk.js';

const databaseService = new DatabaseService();
const dingTalkService = new DingTalkService();
const departmentSyncService = new DepartmentSyncService(databaseService, dingTalkService);

export async function departmentRoutes(fastify: FastifyInstance) {

  /**
   * 获取所有部门列表
   * GET /departments
   */
  fastify.get('/', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_READ)],
  }, async (request, reply) => {
    try {
      const departments = await departmentSyncService.getAllDepartments();
      
      return {
        success: true,
        data: departments,
        total: departments.length
      };
    } catch (error) {
      console.error('获取部门列表失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '获取部门列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });

  /**
   * 搜索部门
   * GET /departments/search?keyword=关键词
   */
  fastify.get('/search', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_READ)],
  }, async (request, reply) => {
    try {
      const { keyword } = request.query as { keyword?: string };
      
      if (!keyword || typeof keyword !== 'string') {
        reply.status(400);
        return {
          success: false,
          message: '请提供搜索关键词'
        };
      }

      const departments = await departmentSyncService.searchDepartments(keyword);
      
      return {
        success: true,
        data: departments,
        total: departments.length,
        keyword
      };
    } catch (error) {
      console.error('搜索部门失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '搜索部门失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });

  /**
   * 获取单个部门信息
   * GET /departments/:deptId
   */
  fastify.get('/:deptId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_READ)],
  }, async (request, reply) => {
    try {
      const { deptId } = request.params as { deptId: string };
      const deptIdNum = parseInt(deptId);
      
      if (isNaN(deptIdNum)) {
        reply.status(400);
        return {
          success: false,
          message: '无效的部门ID'
        };
      }

      const department = await departmentSyncService.getDepartmentInfo(deptIdNum);
      
      if (!department) {
        reply.status(404);
        return {
          success: false,
          message: '部门不存在'
        };
      }

      return {
        success: true,
        data: department
      };
    } catch (error) {
      console.error('获取部门信息失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '获取部门信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });

  /**
   * 获取子部门列表
   * GET /departments/:parentId/children
   */
  fastify.get('/:parentId/children', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_READ)],
  }, async (request, reply) => {
    try {
      const { parentId } = request.params as { parentId: string };
      const parentIdNum = parseInt(parentId);
      
      if (isNaN(parentIdNum)) {
        reply.status(400);
        return {
          success: false,
          message: '无效的父部门ID'
        };
      }

      const subDepartments = await departmentSyncService.getSubDepartments(parentIdNum);
      
      return {
        success: true,
        data: subDepartments,
        total: subDepartments.length,
        parentId: parentIdNum
      };
    } catch (error) {
      console.error('获取子部门列表失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '获取子部门列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });

  /**
   * 手动同步部门数据
   * POST /departments/sync
   */
  fastify.post('/sync', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_UPDATE)],
  }, async (request, reply) => {
    try {
      console.log('开始手动同步部门数据...');
      
      const result = await departmentSyncService.syncAllDepartments();
      
      return {
        success: true,
        message: '部门数据同步完成',
        data: {
          successCount: result.success,
          failedCount: result.failed,
          errors: result.errors
        }
      };
    } catch (error) {
      console.error('同步部门数据失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '同步部门数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });

  /**
   * 获取部门树形结构
   * GET /departments/tree
   */
  fastify.get('/tree', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_READ)],
  }, async (request, reply) => {
    try {
      const allDepartments = await departmentSyncService.getAllDepartments();
      
      // 构建树形结构
      const departmentMap = new Map();
      const rootDepartments: any[] = [];
      
      // 先创建所有部门的映射
      allDepartments.forEach(dept => {
        departmentMap.set(dept.deptId, {
          ...dept,
          children: []
        });
      });
      
      // 构建父子关系
      allDepartments.forEach(dept => {
        const deptWithChildren = departmentMap.get(dept.deptId);
        
        if (dept.parentId === 1 || !departmentMap.has(dept.parentId)) {
          // 根部门或找不到父部门的部门
          rootDepartments.push(deptWithChildren);
        } else {
          // 添加到父部门的children中
          const parent = departmentMap.get(dept.parentId);
          if (parent) {
            parent.children.push(deptWithChildren);
          }
        }
      });
      
      return {
        success: true,
        data: rootDepartments,
        total: allDepartments.length
      };
    } catch (error) {
      console.error('获取部门树形结构失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '获取部门树形结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });

  /**
   * 获取部门同步状态
   * GET /departments/sync/status
   */
  fastify.get('/sync/status', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.DEPARTMENT_READ)],
  }, async (request, reply) => {
    try {
      // 获取最近同步的部门数量和时间
      const recentDepartments = await databaseService.getAllDepartments();
      
      let lastSyncTime: Date | null = null;
      let syncedCount = 0;
      
      if (recentDepartments.length > 0) {
        syncedCount = recentDepartments.length;
        // 这里可以添加获取最后同步时间的逻辑
      }
      
      return {
        success: true,
        data: {
          syncedCount,
          lastSyncTime,
          totalDepartments: recentDepartments.length,
          isUpToDate: true // 这里可以根据实际逻辑判断
        }
      };
    } catch (error) {
      console.error('获取部门同步状态失败:', error);
      reply.status(500);
      return {
        success: false,
        message: '获取部门同步状态失败',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  });
}
