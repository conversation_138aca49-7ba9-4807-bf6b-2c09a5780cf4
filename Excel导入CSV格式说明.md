# Excel导入CSV格式使用说明

## 📁 文件格式要求

由于暂时只支持CSV格式，请按以下步骤准备导入文件：

### 1. 将Excel文件转换为CSV
1. 打开您的Excel文件
2. 点击"文件" → "另存为"
3. 选择文件格式为"CSV (逗号分隔)(*.csv)"
4. 保存文件

### 2. CSV文件格式要求
- 文件编码：UTF-8（推荐）
- 分隔符：逗号(,)
- 第一行必须是表头
- 支持双引号包围包含逗号的字段值

## 📋 CSV表头格式

### 支持的表头名称（中英文均可）

| 字段用途 | 中文表头 | 英文表头 | 必填 | 示例值 |
|----------|----------|----------|------|--------|
| 下单时间 | 下单时间/订单时间 | orderTime | 否 | 2025年1月 |
| 品牌名称 | 品牌名称/品牌 | brandName | **是** | 华帝 |
| 执行项目 | 执行项目/项目类型 | executeProject | 否 | 总项目 |
| 项目名称 | 项目名称/项目 | projectName | **是** | 华帝2501站外推广 |
| 合同签署状况 | 合同签署状况/合同状态 | contractSigningStatus | 否 | 签订中 |
| 合同类型 | 合同类型 | contractType | 否 | 年框-合同 |
| 规划预算 | 规划预算/预算 | planningBudget | **是** | 591.3万 |
| 预计回款月份 | 预计回款月份/回款月份 | expectedPaymentMonth | 否 | 2025年5月 |
| 账期 | 账期 | paymentTermDays | 否 | T+90 |
| 周期 | 周期/执行周期 | period | 否 | 1-2月 |
| 已汇款 | 已汇款/已收款 | amountReceived | 否 | 59.9万 |
| 未付款 | 未付款 | unpaidAmount | 否 | 0.0万 |
| 回款状态 | 回款状态 | reimbursementStatus | 否 | 回款中 |
| 成本 | 成本 | cost | 否 | 52.9万 |
| 预估达人返点 | 预估达人返点/返点 | estimatedInfluencerRebate | 否 | 5.7万 |
| 中介 | 中介 | intermediary | 否 | (空) |

## 📄 CSV文件示例

### 示例文件内容
```csv
下单时间,品牌名称,执行项目,项目名称,合同签署状况,合同类型,规划预算,预计回款月份,账期,周期,已汇款,未付款,回款状态,成本,预估达人返点,中介
2025年1月,华帝,总项目,华帝2501站外推广,签订中,年框-合同,591.3万,2025年5月,T+90,1-2月,59.9万,0.0万,回款中,52.9万,5.7万,
2025年2月,小米,总项目,小米春节营销活动,已签订,单次-合同,300万,2025年4月,T+60,2-3月,100万,200万,收款中,250万,25万,某中介公司
```

### 包含逗号的字段值处理
如果字段值包含逗号，请用双引号包围：
```csv
项目名称,备注
"华帝2501站外推广,包含多个平台",这是一个包含逗号的项目名称
```

## 🚀 API使用方法

### 1. 上传CSV文件导入

```bash
curl -X POST "http://localhost:3000/api/projects/import?createMissingBrands=true&dryRun=false" \
  -H "Authorization: Bearer your-jwt-token" \
  -F "file=@your-file.csv"
```

### 2. 查询参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| createMissingBrands | boolean | true | 自动创建不存在的品牌 |
| defaultExecutorPM | string | 当前用户 | 默认执行PM用户ID |
| defaultContentMediaIds | string | 空 | 默认内容媒介ID（逗号分隔） |
| dryRun | boolean | false | 仅验证不实际创建 |

### 3. 试运行（仅验证）

```bash
curl -X POST "http://localhost:3000/api/projects/import?dryRun=true" \
  -H "Authorization: Bearer your-jwt-token" \
  -F "file=@your-file.csv"
```

## 📊 导入结果

### 成功响应示例
```json
{
  "success": true,
  "data": {
    "success": true,
    "totalRows": 2,
    "successCount": 2,
    "failureCount": 0,
    "errors": [],
    "createdProjects": [
      {
        "row": 1,
        "projectId": "proj_xxx",
        "projectName": "华帝2501站外推广",
        "revenueId": "rev_xxx"
      },
      {
        "row": 2,
        "projectId": "proj_yyy", 
        "projectName": "小米春节营销活动",
        "revenueId": "rev_yyy"
      }
    ],
    "warnings": [
      {
        "row": 1,
        "message": "将自动创建品牌\"华帝\""
      }
    ]
  },
  "message": "导入完成：成功2个，失败0个"
}
```

### 错误响应示例
```json
{
  "success": true,
  "data": {
    "success": false,
    "totalRows": 1,
    "successCount": 0,
    "failureCount": 1,
    "errors": [
      {
        "row": 1,
        "field": "planningBudget",
        "message": "规划预算不能为空"
      }
    ],
    "createdProjects": [],
    "warnings": []
  },
  "message": "导入完成：成功0个，失败1个"
}
```

## 🔧 数据处理逻辑

### 1. 金额处理
- 支持"万"单位：`591.3万` → `5913000`
- 支持普通数字：`5913000` → `5913000`
- 支持逗号分隔：`5,913,000` → `5913000`

### 2. 时间处理
- orderTime作为项目创建时间：`2025年1月` → `2025-01-01`
- period解析为执行周期：`1-2月` → `2025-01-01` 到 `2025-02-28`

### 3. 成本分配
- 总成本均分到三个类别：达人成本、投流成本、其他成本

### 4. 收入创建逻辑
- **已汇款 = 规划预算**：创建1笔已收款收入
- **已汇款 < 规划预算**：创建2笔收入（已收款 + 收款中）
- **已汇款 = 0**：不创建收入记录

## ⚠️ 注意事项

1. **文件格式**：暂时只支持CSV格式，Excel支持将在后续版本中添加
2. **编码格式**：建议使用UTF-8编码保存CSV文件
3. **必填字段**：项目名称、品牌名称、规划预算为必填
4. **权限要求**：需要`project.create`权限
5. **文件大小**：建议单次导入不超过1000行数据
6. **数据验证**：导入前会进行完整的数据验证
7. **品牌创建**：启用`createMissingBrands=true`可自动创建不存在的品牌

## 🎯 最佳实践

1. **先试运行**：使用`dryRun=true`先验证数据格式
2. **小批量导入**：建议分批导入，每批不超过100条
3. **备份数据**：导入前备份现有数据
4. **检查结果**：导入后检查创建的项目和收入记录
5. **错误处理**：根据错误信息修正CSV文件后重新导入

现在您可以将Excel文件另存为CSV格式，然后使用文件上传的方式进行批量导入了！
