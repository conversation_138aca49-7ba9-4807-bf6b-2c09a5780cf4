import { FastifyReply, FastifyRequest } from 'fastify';
import { DatabaseService } from '../services/database.js';
import {
    ContractSigningStatus,
    ContractType,
    DEFAULT_EXCEL_PARSE_CONFIG,
    DocumentType,
    ExcelImportRequest,
    ExcelImportResult,
    ExcelProjectRow,
    ProjectStatus,
    RevenueStatus,
    RevenueType
} from '../types/project.js';

export class ProjectImportController {
  private db: DatabaseService;

  constructor() {
    this.db = new DatabaseService();
  }

  /**
   * 批量导入项目
   */
  async importProjects(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { data, options = {} } = request.body as ExcelImportRequest;
      const userId = (request as any).user?.userid;

      if (!data || !Array.isArray(data) || data.length === 0) {
        return reply.status(400).send({
          success: false,
          message: '导入数据不能为空'
        });
      }

      const result: ExcelImportResult = {
        success: true,
        totalRows: data.length,
        successCount: 0,
        failureCount: 0,
        errors: [],
        createdProjects: [],
        warnings: []
      };

      // 如果是试运行，只验证不创建
      if (options.dryRun) {
        for (let i = 0; i < data.length; i++) {
          const row = data[i];
          const validation = await this.validateRow(row, i + 1, options);
          if (!validation.valid) {
            result.errors.push(...validation.errors);
            result.failureCount++;
          } else {
            result.successCount++;
          }
          if (validation.warnings.length > 0) {
            result.warnings.push(...validation.warnings);
          }
        }
        
        return reply.send({
          success: true,
          data: result,
          message: `验证完成：${result.successCount}行有效，${result.failureCount}行有错误`
        });
      }

      // 实际导入
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNumber = i + 1;

        try {
          // 验证数据
          const validation = await this.validateRow(row, rowNumber, options);
          if (!validation.valid) {
            result.errors.push(...validation.errors);
            result.failureCount++;
            continue;
          }

          if (validation.warnings.length > 0) {
            result.warnings.push(...validation.warnings);
          }

          // 转换数据
          const projectData = await this.convertRowToProject(row, options, userId);
          
          // 创建项目
          const project = await this.db.project.create({
            data: projectData
          });

          // 如果有已汇款金额，创建收入记录
          let revenueId: string | undefined;
          const amountReceived = this.parseAmount(row.amountReceived);
          if (amountReceived > 0) {
            const revenueData = this.createRevenueFromRow(row, project.id, userId);
            const revenue = await this.db.projectRevenue.create({
              data: revenueData
            });
            revenueId = revenue.id;
          }

          result.createdProjects.push({
            row: rowNumber,
            projectId: project.id,
            projectName: project.projectName,
            revenueId
          });
          result.successCount++;

        } catch (error) {
          console.error(`导入第${rowNumber}行时出错:`, error);
          result.errors.push({
            row: rowNumber,
            message: error instanceof Error ? error.message : '未知错误',
            data: row
          });
          result.failureCount++;
        }
      }

      result.success = result.failureCount === 0;

      return reply.send({
        success: true,
        data: result,
        message: `导入完成：成功${result.successCount}个，失败${result.failureCount}个`
      });

    } catch (error) {
      console.error('批量导入项目失败:', error);
      return reply.status(500).send({
        success: false,
        message: '批量导入失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 验证单行数据
   */
  private async validateRow(row: ExcelProjectRow, rowNumber: number, options: any) {
    const errors: any[] = [];
    const warnings: any[] = [];

    // 必填字段验证
    if (!row.projectName?.trim()) {
      errors.push({
        row: rowNumber,
        field: 'projectName',
        message: '项目名称不能为空'
      });
    }

    if (!row.brandName?.trim()) {
      errors.push({
        row: rowNumber,
        field: 'brandName',
        message: '品牌名称不能为空'
      });
    }

    if (!row.planningBudget?.trim()) {
      errors.push({
        row: rowNumber,
        field: 'planningBudget',
        message: '规划预算不能为空'
      });
    }

    // 验证品牌是否存在
    if (row.brandName?.trim()) {
      const brand = await this.db.brand.findFirst({
        where: { name: row.brandName.trim() }
      });
      
      if (!brand && !options.createMissingBrands) {
        errors.push({
          row: rowNumber,
          field: 'brandName',
          message: `品牌"${row.brandName}"不存在，请先创建品牌或启用自动创建选项`
        });
      } else if (!brand && options.createMissingBrands) {
        warnings.push({
          row: rowNumber,
          message: `将自动创建品牌"${row.brandName}"`
        });
      }
    }

    // 验证金额格式
    try {
      this.parseAmount(row.planningBudget);
    } catch (error) {
      errors.push({
        row: rowNumber,
        field: 'planningBudget',
        message: `规划预算格式错误: ${row.planningBudget}`
      });
    }

    if (row.cost?.trim()) {
      try {
        this.parseAmount(row.cost);
      } catch (error) {
        errors.push({
          row: rowNumber,
          field: 'cost',
          message: `成本格式错误: ${row.cost}`
        });
      }
    }

    // 验证枚举值
    if (row.contractSigningStatus && !DEFAULT_EXCEL_PARSE_CONFIG.contractSigningStatusMap[row.contractSigningStatus]) {
      errors.push({
        row: rowNumber,
        field: 'contractSigningStatus',
        message: `不支持的合同签署状态: ${row.contractSigningStatus}`
      });
    }

    if (row.contractType && !DEFAULT_EXCEL_PARSE_CONFIG.contractTypeMap[row.contractType]) {
      errors.push({
        row: rowNumber,
        field: 'contractType',
        message: `不支持的合同类型: ${row.contractType}`
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 解析金额字符串（支持"万"单位）
   */
  private parseAmount(amountStr: string): number {
    if (!amountStr?.trim()) return 0;
    
    const cleanStr = amountStr.trim().replace(/[,，]/g, '');
    
    // 处理"万"单位
    if (cleanStr.includes('万')) {
      const numStr = cleanStr.replace('万', '');
      const num = parseFloat(numStr);
      if (isNaN(num)) {
        throw new Error(`无法解析金额: ${amountStr}`);
      }
      return num * 10000; // 转换为元
    }
    
    const num = parseFloat(cleanStr);
    if (isNaN(num)) {
      throw new Error(`无法解析金额: ${amountStr}`);
    }
    
    return num;
  }

  /**
   * 解析账期天数
   */
  private parsePaymentTermDays(termStr: string): number | undefined {
    if (!termStr?.trim()) return undefined;
    
    const match = termStr.match(/T\+(\d+)/i);
    if (match) {
      return parseInt(match[1]);
    }
    
    return undefined;
  }

  /**
   * 解析执行周期
   */
  private parsePeriod(periodStr: string, orderTime: string) {
    const currentYear = new Date().getFullYear();
    
    // 默认开始时间：当前年份1月1日
    let startDate = new Date(currentYear, 0, 1);
    let endDate = new Date(currentYear, 11, 31);
    
    // 尝试从orderTime解析年份和月份
    if (orderTime) {
      const yearMatch = orderTime.match(/(\d{4})/);
      const monthMatch = orderTime.match(/(\d{1,2})月/);
      
      if (yearMatch) {
        const year = parseInt(yearMatch[1]);
        if (monthMatch) {
          const month = parseInt(monthMatch[1]) - 1; // 月份从0开始
          startDate = new Date(year, month, 1);
          endDate = new Date(year, month + 1, 0); // 月末
        } else {
          startDate = new Date(year, 0, 1);
          endDate = new Date(year, 11, 31);
        }
      }
    }
    
    // 尝试从period解析具体月份范围
    if (periodStr) {
      const rangeMatch = periodStr.match(/(\d{1,2})-(\d{1,2})月/);
      if (rangeMatch) {
        const startMonth = parseInt(rangeMatch[1]) - 1;
        const endMonth = parseInt(rangeMatch[2]) - 1;
        const year = startDate.getFullYear();
        
        startDate = new Date(year, startMonth, 1);
        endDate = new Date(year, endMonth + 1, 0);
      }
    }
    
    return { startDate, endDate };
  }

  /**
   * 转换Excel行数据为项目创建请求
   */
  private async convertRowToProject(row: ExcelProjectRow, options: any, userId: string): Promise<any> {
    // 获取或创建品牌
    let brand = await this.db.brand.findFirst({
      where: { name: row.brandName.trim() }
    });

    if (!brand && options.createMissingBrands) {
      brand = await this.db.brand.create({
        data: {
          name: row.brandName.trim(),
          status: 'active',
          createdBy: userId,
          updatedBy: userId
        }
      });
    }

    if (!brand) {
      throw new Error(`品牌"${row.brandName}"不存在`);
    }

    // 解析金额
    const planningBudget = this.parseAmount(row.planningBudget);
    const totalCost = row.cost ? this.parseAmount(row.cost) : 0;
    const estimatedInfluencerRebate = row.estimatedInfluencerRebate ? this.parseAmount(row.estimatedInfluencerRebate) : 0;

    // 解析周期
    const period = this.parsePeriod(row.period, row.orderTime);

    // 解析账期
    const paymentTermDays = this.parsePaymentTermDays(row.paymentTermDays);

    // 解析预计回款月份
    let expectedPaymentMonth: string | undefined;
    if (row.expectedPaymentMonth) {
      const match = row.expectedPaymentMonth.match(/(\d{4})年(\d{1,2})月/);
      if (match) {
        const year = match[1];
        const month = match[2].padStart(2, '0');
        expectedPaymentMonth = `${year}-${month}`;
      }
    }

    // 映射枚举值
    const documentType = DEFAULT_EXCEL_PARSE_CONFIG.documentTypeMap[row.executeProject] || DocumentType.PROJECT_INITIATION;
    const contractType = DEFAULT_EXCEL_PARSE_CONFIG.contractTypeMap[row.contractType] || ContractType.SINGLE;
    const contractSigningStatus = DEFAULT_EXCEL_PARSE_CONFIG.contractSigningStatusMap[row.contractSigningStatus] || ContractSigningStatus.PENDING;

    // 计算利润
    const profit = planningBudget - totalCost + estimatedInfluencerRebate;
    const grossMargin = planningBudget > 0 ? (profit / planningBudget) * 100 : 0;

    const projectData = {
      documentType,
      brandId: brand.id,
      projectName: row.projectName.trim(),
      period: {
        startDate: period.startDate,
        endDate: period.endDate
      },
      budget: {
        planningBudget,
        influencerBudget: 0,
        adBudget: 0,
        otherBudget: 0
      },
      cost: {
        influencerCost: 0,
        adCost: 0,
        otherCost: totalCost,
        estimatedInfluencerRebate
      },
      profit: {
        profit,
        grossMargin
      },
      executorPM: options.defaultExecutorPM || userId,
      contentMediaIds: options.defaultContentMediaIds || [],
      contractType,
      contractSigningStatus,
      expectedPaymentMonth,
      paymentTermDays,
      settlementRules: '根据合同约定执行',
      kpi: '根据项目需求制定',
      attachments: [],
      status: ProjectStatus.ACTIVE,
      createdBy: userId,
      updatedBy: userId
    };

    return projectData;
  }

  /**
   * 从Excel行数据创建收入记录
   */
  private createRevenueFromRow(row: ExcelProjectRow, projectId: string, userId: string): any {
    const amountReceived = this.parseAmount(row.amountReceived);
    const revenueStatus = DEFAULT_EXCEL_PARSE_CONFIG.revenueStatusMap[row.reimbursementStatus] || RevenueStatus.RECEIVING;

    const revenueData = {
      title: `${row.projectName} - 项目收入`,
      revenueType: RevenueType.PROJECT_INCOME,
      status: revenueStatus,
      plannedAmount: amountReceived,
      actualAmount: revenueStatus === RevenueStatus.RECEIVED ? amountReceived : undefined,
      plannedDate: new Date(),
      confirmedDate: revenueStatus === RevenueStatus.RECEIVED ? new Date() : undefined,
      receivedDate: revenueStatus === RevenueStatus.RECEIVED ? new Date() : undefined,
      milestone: '项目收入',
      notes: `从Excel导入的收入记录，原始回款状态：${row.reimbursementStatus}`,
      projectId,
      createdBy: userId,
      updatedBy: userId
    };

    return revenueData;
  }

  /**
   * 获取导入模板
   */
  async getImportTemplate(request: FastifyRequest, reply: FastifyReply) {
    try {
      const template = {
        headers: [
          'orderTime',
          'brandName',
          'executeProject',
          'projectName',
          'contractSigningStatus',
          'contractType',
          'planningBudget',
          'expectedPaymentMonth',
          'paymentTermDays',
          'period',
          'amountReceived',
          'unpaidAmount',
          'reimbursementStatus',
          'cost',
          'estimatedInfluencerRebate',
          'intermediary'
        ],
        headerLabels: {
          orderTime: '下单时间',
          brandName: '品牌名称',
          executeProject: '执行项目',
          projectName: '项目名称',
          contractSigningStatus: '合同签署状况',
          contractType: '合同类型',
          planningBudget: '规划预算',
          expectedPaymentMonth: '预计回款月份',
          paymentTermDays: '账期',
          period: '周期',
          amountReceived: '已汇款',
          unpaidAmount: '未付款',
          reimbursementStatus: '回款状态',
          cost: '成本',
          estimatedInfluencerRebate: '预估达人返点',
          intermediary: '中介'
        },
        example: {
          orderTime: '2025年1月',
          brandName: '华帝',
          executeProject: '总项目',
          projectName: '华帝2501站外推广',
          contractSigningStatus: '签订中',
          contractType: '年框-合同',
          planningBudget: '591.3万',
          expectedPaymentMonth: '2025年5月',
          paymentTermDays: 'T+90',
          period: '1-2月',
          amountReceived: '59.9万',
          unpaidAmount: '0.0万',
          reimbursementStatus: '回款中',
          cost: '52.9万',
          estimatedInfluencerRebate: '5.7万',
          intermediary: ''
        },
        enums: {
          contractSigningStatus: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.contractSigningStatusMap),
          contractType: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.contractTypeMap),
          executeProject: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.documentTypeMap),
          reimbursementStatus: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.revenueStatusMap)
        }
      };

      return reply.send({
        success: true,
        data: template,
        message: '获取导入模板成功'
      });

    } catch (error) {
      console.error('获取导入模板失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取导入模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
}
