# Excel导入逻辑说明

## 🔄 更新的导入逻辑

根据您的需求，我已经更新了Excel导入功能，主要改进包括：

### 1. 📅 时间处理优化

#### orderTime 作为创建时间
- **原逻辑**: orderTime 仅用于解析年份
- **新逻辑**: orderTime 直接作为项目的 `createdAt` 时间
- **示例**: "2025年1月" → 2025-01-01 00:00:00

#### period 精确解析
- **解析规则**: 基于 orderTime 的年份 + period 的月份范围
- **示例**: 
  - orderTime: "2025年1月", period: "1-2月" → 2025-01-01 到 2025-02-28
  - orderTime: "2025年1月", period: "3月" → 2025-03-01 到 2025-03-31

### 2. 💰 成本分配优化

#### 成本均分策略
- **原逻辑**: 所有成本归入 `otherCost`
- **新逻辑**: 总成本均分到三个类别
- **分配方式**:
  ```typescript
  const costPerCategory = totalCost / 3;
  influencerCost = costPerCategory;  // 达人成本
  adCost = costPerCategory;          // 投流成本  
  otherCost = costPerCategory;       // 其他成本
  ```

### 3. 🎯 收入记录创建逻辑

#### 智能收入创建策略

根据 `planningBudget` 和 `amountReceived` 的关系：

##### 情况1: 已汇款 = 规划预算
```
planningBudget: 10万
amountReceived: 10万
→ 创建1笔收入：10万（已收款）
```

##### 情况2: 已汇款 < 规划预算  
```
planningBudget: 10万
amountReceived: 5万
→ 创建2笔收入：
  - 第1笔：5万（已收款）
  - 第2笔：5万（收款中）
```

##### 情况3: 无已汇款
```
planningBudget: 10万
amountReceived: 0万
→ 不创建收入记录
```

## 📊 数据转换示例

### 您的示例数据处理

**输入数据**:
```
orderTime: "2025年1月"
brandName: "华帝"
projectName: "华帝2501站外推广"
planningBudget: "591.3万"
amountReceived: "59.9万"
cost: "52.9万"
period: "1-2月"
```

**转换结果**:

#### 项目信息
```typescript
{
  projectName: "华帝2501站外推广",
  createdAt: new Date("2025-01-01"),
  period: {
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-02-28")
  },
  budget: {
    planningBudget: 5913000  // 591.3万
  },
  cost: {
    influencerCost: 176333,  // 52.9万 ÷ 3
    adCost: 176333,          // 52.9万 ÷ 3
    otherCost: 176334,       // 52.9万 ÷ 3 (余数)
    estimatedInfluencerRebate: 57000  // 5.7万
  }
}
```

#### 收入记录（2笔）
```typescript
[
  {
    title: "华帝2501站外推广 - 项目收入（已收款）",
    status: "RECEIVED",
    plannedAmount: 599000,    // 59.9万
    actualAmount: 599000,     // 59.9万
    confirmedDate: "2025-01-01",
    receivedDate: "2025-01-01"
  },
  {
    title: "华帝2501站外推广 - 项目收入（收款中）", 
    status: "RECEIVING",
    plannedAmount: 5314000,   // 531.4万 (591.3-59.9)
    actualAmount: null,
    confirmedDate: null
  }
]
```

## 🔧 技术实现细节

### 金额解析
```typescript
parseAmount("591.3万") → 5913000
parseAmount("59.9万") → 599000
parseAmount("52.9万") → 529000
```

### 日期解析
```typescript
parseOrderTime("2025年1月") → new Date("2025-01-01")
parsePeriod("1-2月", "2025年1月") → {
  startDate: new Date("2025-01-01"),
  endDate: new Date("2025-02-28")
}
```

### 账期解析
```typescript
parsePaymentTermDays("T+90") → 90
```

## 🎯 使用方法

### API调用示例
```javascript
const importData = {
  data: [
    {
      orderTime: "2025年1月",
      brandName: "华帝", 
      executeProject: "总项目",
      projectName: "华帝2501站外推广",
      contractSigningStatus: "签订中",
      contractType: "年框-合同",
      planningBudget: "591.3万",
      expectedPaymentMonth: "2025年5月",
      paymentTermDays: "T+90",
      period: "1-2月",
      amountReceived: "59.9万",
      unpaidAmount: "0.0万",
      reimbursementStatus: "回款中",
      cost: "52.9万",
      estimatedInfluencerRebate: "5.7万",
      intermediary: ""
    }
  ],
  options: {
    createMissingBrands: true,
    defaultExecutorPM: "your-user-id",
    defaultContentMediaIds: ["content-user-id"]
  }
};

// 发送导入请求
fetch('/api/projects/import', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(importData)
});
```

### 预期结果
```json
{
  "success": true,
  "data": {
    "totalRows": 1,
    "successCount": 1,
    "failureCount": 0,
    "createdProjects": [
      {
        "row": 1,
        "projectId": "proj_xxx",
        "projectName": "华帝2501站外推广",
        "revenueId": "rev_xxx"
      }
    ],
    "warnings": [
      {
        "row": 1,
        "message": "将自动创建品牌\"华帝\""
      }
    ]
  }
}
```

## ✅ 验证清单

导入成功后，请验证：

1. **项目创建**: 项目名称、预算、成本、周期正确
2. **品牌创建**: 如果品牌不存在，已自动创建
3. **收入记录**: 
   - 第1笔：59.9万，状态为"已收款"
   - 第2笔：531.4万，状态为"收款中"
4. **时间设置**: 项目创建时间为2025年1月
5. **成本分配**: 三类成本均分52.9万

这个新的导入逻辑完全符合您的历史项目数据处理需求！
