// 项目管理相关类型定义
import { ApprovalStatus } from './approval.js';

// 单据类型枚举
export enum DocumentType {
  PROJECT_INITIATION = 'project_initiation', // 项目立项表
  PROJECT_PROPOSAL = 'project_proposal',     // 项目提案
  PROJECT_PLAN = 'project_plan',             // 项目计划
  PROJECT_EXECUTION = 'project_execution',   // 项目执行
  PROJECT_SUMMARY = 'project_summary',       // 项目总结
}

// 合同类型枚举
export enum ContractType {
  ANNUAL_FRAME = 'annual_frame',     // 年框
  QUARTERLY_FRAME = 'quarterly_frame', // 季框
  SINGLE = 'single',                 // 单次
  PO_ORDER = 'po_order',            // PO单
  JING_TASK = 'jing_task',          // 京任务
}

// 合同签署状态枚举
export enum ContractSigningStatus {
  NO_CONTRACT = 'no_contract',       // 无合同
  SIGNED = 'signed',                 // 已签订
  SIGNING = 'signing',               // 签订中
  PENDING = 'pending',               // 待定
}

// 收入状态枚举
export enum RevenueStatus {
  RECEIVING = 'receiving',   // 收款中
  RECEIVED = 'received',     // 已收款
  CANCELLED = 'cancelled',   // 已取消
}

// 收入类型枚举
export enum RevenueType {
  INFLUENCER_INCOME = 'influencer_income', // 达人收入
  PROJECT_INCOME = 'project_income',       // 项目收入
  OTHER = 'other',           // 其他收入
}

// 确认收入请求接口
export interface ConfirmRevenueRequest {
  actualAmount: number;
  confirmedDate?: string;
  notes?: string;
}

// 批量确认收入请求接口
export interface BatchConfirmRevenueRequest {
  revenues: Array<{
    id: string;
    actualAmount: number;
    confirmedDate?: string;
    notes?: string;
  }>;
}

// 批量确认收入响应接口
export interface BatchConfirmRevenueResponse {
  successCount: number;
  failureCount: number;
  results: Array<{
    id: string;
    success: boolean;
    data?: any;
    error?: string;
  }>;
}

// 供应商状态枚举
export enum SupplierStatus {
  ACTIVE = 'active',         // 活跃
  INACTIVE = 'inactive',     // 停用
  PENDING = 'pending',       // 待审核
  BLACKLISTED = 'blacklisted', // 黑名单
}

// 服务类型枚举
export enum ServiceType {
  INFLUENCER = 'influencer', // 达人服务
  ADVERTISING = 'advertising', // 投流服务
  OTHER = 'other',           // 其他服务
}

// 税率枚举
export enum TaxRate {
  SPECIAL_1 = 'special_1',   // 专票1%
  SPECIAL_3 = 'special_3',   // 专票3%
  SPECIAL_6 = 'special_6',   // 专票6%
  GENERAL = 'general',       // 普票
}

// 周预算状态枚举
export enum WeeklyBudgetStatus {
  DRAFT = 'draft',           // 草稿
  APPROVED = 'approved',     // 已批准
  EXECUTING = 'executing',   // 执行中
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled',   // 已取消
}

// 项目状态枚举
export enum ProjectStatus {
  DRAFT = 'draft',           // 草稿
  ACTIVE = 'active',         // 进行中
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled',   // 已取消
}

// 品牌信息
export interface Brand {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

// 用户信息（简化版，用于选择）
export interface User {
  userid: string;
  name: string;
  avatar?: string;
  department?: string;
}

// 项目执行周期
export interface ProjectPeriod {
  startDate: Date;
  endDate: Date;
}

// 项目预算信息
export interface ProjectBudget {
  planningBudget: number;      // 项目规划预算
  influencerBudget: number;    // 达人预算
  adBudget: number;           // 投流预算
  otherBudget: number;        // 其他预算
}

// 项目成本信息
export interface ProjectCost {
  influencerCost: number;      // 达人成本
  adCost: number;             // 投流成本
  otherCost: number;          // 其他成本
  estimatedInfluencerRebate: number; // 预估达人返点（注意：这是收入性质，减少总成本）
}

// 项目利润信息（自动计算）
export interface ProjectProfit {
  profit: number;             // 项目利润 = 规划预算 - 成本 + 返点
  grossMargin: number;        // 项目毛利 = 项目利润 / 规划预算
}

// 附件信息
export interface Attachment {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
}

// 供应商信息
export interface Supplier {
  id: string;
  name: string;                     // 供应商名称
  shortName?: string;               // 简称
  code?: string;                    // 供应商编码
  contactPerson?: string;           // 联系人
  contactPhone?: string;            // 联系电话
  contactEmail?: string;            // 联系邮箱
  address?: string;                 // 地址
  taxNumber?: string;               // 税号
  bankAccount?: string;             // 银行账号
  bankName?: string;                // 开户银行
  legalPerson?: string;             // 法人代表
  serviceTypes: ServiceType[];      // 服务类型
  preferredTaxRate?: TaxRate;       // 首选税率
  creditLimit?: number;             // 信用额度
  paymentTerms?: string;            // 付款条件
  status: SupplierStatus;           // 供应商状态
  rating?: number;                  // 评级 (1-5)
  notes?: string;                   // 备注
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 项目周预算信息
export interface WeeklyBudget {
  id: string;
  title: string;                    // 预算标题
  weekStartDate: Date;              // 周开始日期
  weekEndDate: Date;                // 周结束日期
  weekNumber: number;               // 第几周
  year: number;                     // 年份
  serviceType: ServiceType;         // 服务类型
  serviceContent: string;           // 服务内容描述
  remarks?: string;                 // 备注
  contractAmount: number;           // 合同金额
  taxRate: TaxRate;                 // 税率
  paidAmount: number;               // 已付金额
  remainingAmount: number;          // 剩余金额
  status: WeeklyBudgetStatus;       // 预算状态
  approvalStatus: ApprovalStatus;   // 审批状态
  approvalAmount?: number;          // 审批金额
  approvalReason?: string;          // 审批原因
  projectId: string;                // 关联项目ID
  supplierId?: string;              // 关联供应商ID
  supplier?: Supplier;              // 供应商信息
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 项目收入信息
export interface ProjectRevenue {
  id: string;
  title: string;                    // 收入标题/描述
  revenueType: RevenueType;         // 收入类型
  status: RevenueStatus;            // 收入状态
  plannedAmount: number;            // 预计收入金额
  actualAmount?: number;            // 实际收入金额
  invoiceAmount?: number;           // 开票金额
  plannedDate: Date;                // 预计收入时间
  confirmedDate?: Date;             // 确认收入时间
  invoiceDate?: Date;               // 开票时间
  receivedDate?: Date;              // 实际收款时间
  milestone?: string;               // 里程碑描述
  invoiceNumber?: string;           // 发票号码
  paymentTerms?: string;            // 付款条件
  notes?: string;                   // 备注说明
  projectId: string;                // 关联项目ID
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 项目主表
export interface Project {
  id: string;
  documentType: DocumentType;
  brandId: string;
  brand?: Brand;                    // 关联的品牌信息
  projectName: string;
  period: ProjectPeriod;
  budget: ProjectBudget;
  cost: ProjectCost;
  profit: ProjectProfit;            // 自动计算字段
  executorPM: string;               // 执行PM的用户ID
  executorPMInfo?: User;            // 执行PM的用户信息
  contentMediaIds: string[];        // 内容媒介的用户ID数组
  contentMediaInfo?: User[];        // 内容媒介的用户信息
  contractType: ContractType;
  contractSigningStatus: ContractSigningStatus; // 合同签署情况
  settlementRules: string;          // 项目结算规则（富文本）
  kpi: string;                      // KPI（富文本）

  // 财务回款信息
  expectedPaymentMonth?: string;    // 预计回款月份 (YYYY-MM格式，如2024-03)
  paymentTermDays?: number;         // 账期天数 (如180表示T+180)

  attachments: Attachment[];        // 附件列表
  revenues?: ProjectRevenue[];      // 项目收入列表
  weeklyBudgets?: WeeklyBudget[];   // 项目周预算列表
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 创建项目请求
export interface CreateProjectRequest {
  documentType: DocumentType;
  brandId: string;
  projectName: string;
  period: ProjectPeriod;
  budget: ProjectBudget;
  cost: ProjectCost;
  executorPM: string;
  contentMediaIds: string[];
  contractType: ContractType;
  contractSigningStatus?: ContractSigningStatus; // 合同签署情况（可选，默认为PENDING）
  settlementRules: string;
  kpi: string;

  // 财务回款信息
  expectedPaymentMonth?: string;    // 预计回款月份 (YYYY-MM格式，如2024-03)
  paymentTermDays?: number;         // 账期天数 (如180表示T+180)

  // 附件信息
  attachmentIds?: string[];         // 附件ID列表
}

// 更新项目请求
export interface UpdateProjectRequest extends Partial<CreateProjectRequest> {
  id: string;
}

// 创建项目收入请求
export interface CreateProjectRevenueRequest {
  title: string;
  revenueType: RevenueType;
  plannedAmount: number;
  plannedDate?: Date | string | null;
  milestone?: string;
  paymentTerms?: string;
  notes?: string;
}

// 更新项目收入请求
export interface UpdateProjectRevenueRequest extends Partial<CreateProjectRevenueRequest> {
  id: string;
  status?: RevenueStatus;
  actualAmount?: number;
  invoiceAmount?: number;
  confirmedDate?: Date | string;
  invoiceDate?: Date | string;
  receivedDate?: Date | string;
  invoiceNumber?: string;
}

// 创建供应商请求
export interface CreateSupplierRequest {
  name: string;
  shortName?: string;
  code?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  taxNumber?: string;
  bankAccount?: string;
  bankName?: string;
  legalPerson?: string;
  serviceTypes: ServiceType[];
  preferredTaxRate?: TaxRate;
  creditLimit?: number;
  paymentTerms?: string;
  rating?: number;
  notes?: string;
}

// 更新供应商请求
export interface UpdateSupplierRequest extends Partial<CreateSupplierRequest> {
  id: string;
  status?: SupplierStatus;
}

// 创建周预算请求
export interface CreateWeeklyBudgetRequest {
  title: string;
  weekStartDate: Date | string;
  weekEndDate: Date | string;
  serviceType: ServiceType;
  serviceContent: string;
  remarks?: string;
  contractAmount: number;
  taxRate: TaxRate;
  supplierId?: string;
}

// 更新周预算请求
export interface UpdateWeeklyBudgetRequest extends Partial<CreateWeeklyBudgetRequest> {
  id: string;
  status?: WeeklyBudgetStatus;
  paidAmount?: number;
}

// 项目查询参数
export interface ProjectQueryParams {
  page?: number;
  pageSize?: number;
  documentType?: DocumentType;
  brandId?: string;
  contractType?: ContractType;
  contractSigningStatus?: ContractSigningStatus; // 合同签署状态过滤
  executorPM?: string;
  status?: Project['status'];
  keyword?: string;                 // 项目名称关键字搜索
  startDate?: Date;                // 项目开始时间范围
  endDate?: Date;                  // 项目结束时间范围
  sortBy?: 'createdAt' | 'updatedAt' | 'projectName' | 'profit';
  sortOrder?: 'asc' | 'desc';
}

// 项目列表响应
export interface ProjectListResponse {
  projects: Project[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 品牌创建请求
export interface CreateBrandRequest {
  name: string;
  description?: string;
  logo?: string;
  status?: Brand['status'];
}

// 品牌更新请求
export interface UpdateBrandRequest extends Partial<CreateBrandRequest> {
  id: string;
  status?: Brand['status'];
}

// 品牌查询参数
export interface BrandQueryParams {
  page?: number;
  pageSize?: number;
  status?: Brand['status'];
  keyword?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 品牌列表响应
export interface BrandListResponse {
  brands: Brand[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 文件上传响应
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
}

// 项目变更记录相关类型

// 变更类型枚举
export enum ChangeType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  STATUS_CHANGE = 'STATUS_CHANGE',
  APPROVAL = 'APPROVAL',
  ATTACHMENT = 'ATTACHMENT'
}

// 变更类型中文映射
export const ChangeTypeLabels: Record<ChangeType, string> = {
  [ChangeType.CREATE]: '创建',
  [ChangeType.UPDATE]: '更新',
  [ChangeType.DELETE]: '删除',
  [ChangeType.STATUS_CHANGE]: '状态变更',
  [ChangeType.APPROVAL]: '审批操作',
  [ChangeType.ATTACHMENT]: '附件操作'
};

// 项目变更记录
export interface ProjectChangeLog {
  id: string;
  changeType: ChangeType;
  changeTitle: string;
  changeDetails?: any; // JSON格式的变更详情
  beforeData?: any; // 变更前数据
  afterData?: any; // 变更后数据
  changedFields: string[]; // 变更的字段列表
  operatorId: string; // 操作人员钉钉用户ID
  operatorName: string; // 操作人员姓名
  operatorIP?: string; // 操作人员IP地址
  userAgent?: string; // 用户代理信息
  reason?: string; // 变更原因
  description?: string; // 变更描述
  projectId: string; // 关联项目ID
  project?: {
    id: string;
    projectName: string;
    status: string;
    brand?: {
      id: string;
      name: string;
    };
  }; // 关联项目信息（简化版）
  createdAt: Date; // 创建时间
}

// 创建变更记录请求
export interface CreateChangeLogRequest {
  changeType: ChangeType;
  changeTitle: string;
  changeDetails?: any;
  beforeData?: any;
  afterData?: any;
  changedFields: string[];
  reason?: string;
  description?: string;
  projectId: string;
  operatorId: string;
  operatorName: string;
  operatorIP?: string;
  userAgent?: string;
}

// 变更记录查询参数
export interface ChangeLogQueryParams {
  projectId?: string;
  operatorId?: string;
  changeType?: ChangeType;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
  sortBy?: 'createdAt' | 'changeType';
  sortOrder?: 'asc' | 'desc';
}

// 变更记录列表响应
export interface ChangeLogListResponse {
  changeLogs: ProjectChangeLog[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  timestamp?: string;
}

// 项目收入查询参数
export interface ProjectRevenueQueryParams {
  page?: number;
  pageSize?: number;
  projectId?: string;
  status?: RevenueStatus;
  revenueType?: RevenueType;
  startDate?: string;
  endDate?: string;
  sortBy?: 'plannedDate' | 'plannedAmount' | 'actualAmount' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// 项目收入列表响应
export interface ProjectRevenueListResponse {
  revenues: ProjectRevenue[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 供应商查询参数
export interface SupplierQueryParams {
  page?: number;
  pageSize?: number;
  status?: SupplierStatus;
  serviceType?: ServiceType;
  keyword?: string;
  sortBy?: 'name' | 'createdAt' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

// 供应商列表响应
export interface SupplierListResponse {
  suppliers: Supplier[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 周预算查询参数
export interface WeeklyBudgetQueryParams {
  page?: number;
  pageSize?: number;
  projectId?: string;
  supplierId?: string;
  serviceType?: ServiceType;
  status?: WeeklyBudgetStatus;
  year?: number;
  weekNumber?: number;
  startDate?: string;
  endDate?: string;
  sortBy?: 'weekStartDate' | 'contractAmount' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// 周预算列表响应
export interface WeeklyBudgetListResponse {
  weeklyBudgets: WeeklyBudget[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 收入统计信息
export interface RevenueStats {
  totalPlannedRevenue: number;      // 总预计收入
  totalActualRevenue: number;       // 总实际收入
  totalInvoicedRevenue: number;     // 总开票收入
  totalReceivedRevenue: number;     // 总已收款收入
  revenueByStatus: Array<{
    status: RevenueStatus;
    count: number;
    totalAmount: number;
  }>;
  revenueByType: Array<{
    type: RevenueType;
    count: number;
    totalAmount: number;
  }>;
  monthlyRevenueTrend: Array<{
    month: string;
    plannedAmount: number;
    actualAmount: number;
  }>;
}

// 周预算统计信息
export interface WeeklyBudgetStats {
  totalBudgets: number;             // 总预算数
  totalContractAmount: number;      // 总合同金额
  totalPaidAmount: number;          // 总已付金额
  totalRemainingAmount: number;     // 总剩余金额
  budgetsByServiceType: Array<{
    serviceType: ServiceType;
    count: number;
    totalAmount: number;
  }>;
  budgetsByStatus: Array<{
    status: WeeklyBudgetStatus;
    count: number;
    totalAmount: number;
  }>;
  budgetsBySupplier: Array<{
    supplierId: string;
    supplierName: string;
    count: number;
    totalAmount: number;
  }>;
  weeklyTrend: Array<{
    week: string;
    contractAmount: number;
    paidAmount: number;
  }>;
}

// 品牌财务汇总信息
export interface BrandFinancialSummary {
  brandId: string;
  brandName: string;
  orderAmount: number;              // 品牌下单金额 (项目规划预算总和)
  executedAmount: number;           // 已执行金额 (已完成项目的实际支出)
  executingAmount: number;          // 执行中项目金额 (进行中项目的预算)
  estimatedProfit: number;          // 预估毛利 (预计利润总和)
  estimatedProfitMargin: number;    // 预估毛利率 (预估毛利/品牌下单金额)
  receivedAmount: number;           // 已回款 (已收到的收入)
  unreceivedAmount: number;         // 未回款 (计划收入-已回款)
  paidProjectAmount: number;        // 已支付项目金额 (周预算中已支付金额总和)
  unpaidProjectAmount: number;      // 未支付项目金额 (周预算中未支付金额总和)
  remarks?: string;                 // 备注字段
  projectCount: number;             // 项目数量
  activeProjectCount: number;       // 进行中项目数量
  completedProjectCount: number;    // 已完成项目数量
}

// 品牌财务详细信息
export interface BrandFinancialDetail {
  brandInfo: {
    id: string;
    name: string;
    description?: string;
    logo?: string;
  };
  summary: BrandFinancialSummary;
  projects: Array<{
    id: string;
    projectName: string;
    status: ProjectStatus;
    documentType: DocumentType;
    contractType: ContractType;
    period: {
      startDate: Date;
      endDate: Date;
    };
    budget: {
      planningBudget: number;
      totalBudget: number;
    };
    cost: {
      totalCost: number;
      estimatedInfluencerRebate: number;
    };
    profit: {
      profit: number;
      grossMargin: number;
    };
    revenue: {
      plannedAmount: number;
      receivedAmount: number;
      unreceivedAmount: number;
    };
    weeklyBudgets: {
      totalContractAmount: number;
      paidAmount: number;
      unpaidAmount: number;
    };
    executorPM: string;
    executorPMInfo?: {
      userid: string;
      name: string;
      department: string;
    };
  }>;
  revenueAnalysis: {
    totalPlannedRevenue: number;
    totalReceivedRevenue: number;
    revenueByStatus: Array<{
      status: RevenueStatus;
      count: number;
      totalAmount: number;
    }>;
    monthlyTrend: Array<{
      month: string;
      plannedAmount: number;
      receivedAmount: number;
    }>;
  };
  costAnalysis: {
    totalWeeklyBudgets: number;
    totalPaidAmount: number;
    totalUnpaidAmount: number;
    budgetsByServiceType: Array<{
      serviceType: ServiceType;
      count: number;
      totalAmount: number;
      paidAmount: number;
    }>;
  };
}

// 财务报表查询参数
export interface FinancialReportQueryParams {
  brandId?: string;                 // 品牌ID过滤
  startDate?: Date | string;        // 开始日期
  endDate?: Date | string;          // 结束日期
  projectStatus?: ProjectStatus[];  // 项目状态过滤
  includeCompleted?: boolean;       // 是否包含已完成项目
  includeCancelled?: boolean;       // 是否包含已取消项目
}

// 财务报表响应
export interface FinancialReportResponse {
  summary: {
    totalBrands: number;
    totalOrderAmount: number;
    totalExecutedAmount: number;
    totalEstimatedProfit: number;
    totalReceivedAmount: number;
    totalPaidAmount: number;
    overallProfitMargin: number;
  };
  brands: BrandFinancialSummary[];
  generatedAt: Date;
  reportPeriod: {
    startDate?: Date;
    endDate?: Date;
  };
}

// 供应商统计信息
export interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  suppliersByServiceType: Array<{
    serviceType: ServiceType;
    count: number;
  }>;
  suppliersByRating: Array<{
    rating: number;
    count: number;
  }>;
}

// 项目统计信息
export interface ProjectStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalBudget: number;
  totalProfit: number;
  averageGrossMargin: number;
  revenueStats: RevenueStats;       // 收入统计
  weeklyBudgetStats: WeeklyBudgetStats; // 周预算统计
  supplierStats: SupplierStats;     // 供应商统计
  projectsByBrand: Array<{
    brandId: string;
    brandName: string;
    count: number;
    totalBudget: number;
  }>;
  projectsByContractType: Array<{
    contractType: ContractType;
    count: number;
    totalBudget: number;
  }>;
}

// 下拉选项类型
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// 表单验证错误
export interface ValidationError {
  field: string;
  message: string;
}

// 批量操作请求
export interface BatchOperationRequest {
  ids: string[];
  operation: 'delete' | 'activate' | 'deactivate' | 'complete' | 'cancel';
}

// 导出请求
export interface ExportRequest {
  format: 'excel' | 'csv';
  filters?: ProjectQueryParams;
  fields?: string[];
}

// 项目模板
export interface ProjectTemplate {
  id: string;
  name: string;
  description?: string;
  documentType: DocumentType;
  contractType: ContractType;
  defaultBudget?: Partial<ProjectBudget>;
  defaultCost?: Partial<ProjectCost>;
  settlementRulesTemplate?: string;
  kpiTemplate?: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}
