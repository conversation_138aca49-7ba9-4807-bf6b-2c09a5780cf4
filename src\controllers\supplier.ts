import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ProjectService } from '../services/project.js';
import { SupplierService } from '../services/supplier.js';
import {
  ServiceType,
  SupplierStatus,
  TaxRate
} from '../types/project.js';

// 验证模式
const createSupplierSchema = z.object({
  name: z.string().min(1, '供应商名称不能为空').max(200, '供应商名称不能超过200字符'),
  shortName: z.string().max(100, '简称不能超过100字符').optional(),
  code: z.string().max(50, '供应商编码不能超过50字符').optional(),
  contactPerson: z.string().max(100, '联系人不能超过100字符').optional(),
  contactPhone: z.string().max(20, '联系电话不能超过20字符').optional(),
  contactEmail: z.string().email('邮箱格式不正确').max(100, '邮箱不能超过100字符').optional(),
  address: z.string().optional(),
  taxNumber: z.string().max(50, '税号不能超过50字符').optional(),
  bankAccount: z.string().max(50, '银行账号不能超过50字符').optional(),
  bankName: z.string().max(200, '开户银行不能超过200字符').optional(),
  legalPerson: z.string().max(100, '法人代表不能超过100字符').optional(),
  serviceTypes: z.array(z.nativeEnum(ServiceType)).min(1, '至少选择一种服务类型'),
  preferredTaxRate: z.nativeEnum(TaxRate).optional(),
  creditLimit: z.number().min(0, '信用额度不能为负数').optional(),
  paymentTerms: z.string().optional(),
  rating: z.number().min(1).max(5, '评级必须在1-5之间').optional(),
  notes: z.string().optional(),
});

const updateSupplierSchema = z.object({
  name: z.string().min(1).max(200).optional(),
  shortName: z.string().max(100).optional(),
  code: z.string().max(50).optional(),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  contactEmail: z.string().email().max(100).optional(),
  address: z.string().optional(),
  taxNumber: z.string().max(50).optional(),
  bankAccount: z.string().max(50).optional(),
  bankName: z.string().max(200).optional(),
  legalPerson: z.string().max(100).optional(),
  serviceTypes: z.array(z.nativeEnum(ServiceType)).optional(),
  preferredTaxRate: z.nativeEnum(TaxRate).optional(),
  creditLimit: z.number().min(0).optional(),
  paymentTerms: z.string().optional(),
  status: z.nativeEnum(SupplierStatus).optional(),
  rating: z.number().min(1).max(5).optional(),
  notes: z.string().optional(),
});

const supplierQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  status: z.nativeEnum(SupplierStatus).optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  keyword: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'rating']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export class SupplierController {
  private projectService: ProjectService;
  private supplierService: SupplierService;

  constructor() {
    this.projectService = new ProjectService();
    this.supplierService = new SupplierService();

  }

  /**
   * 创建供应商
   */
  async createSupplier(request: FastifyRequest, reply: FastifyReply) {
    try {
      const supplierData = createSupplierSchema.parse(request.body);
      
      // 获取当前用户ID
      const createdBy = 'current-user'; // 实际应该从认证信息中获取

      const supplier = await this.projectService.createSupplier(supplierData, createdBy);

      return reply.send({
        success: true,
        data: supplier,
        message: '创建供应商成功'
      });
    } catch (error) {
      console.error('创建供应商失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '创建供应商失败'
      });
    }
  }

  /**
   * 获取供应商列表
   */
  async getSuppliers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = supplierQuerySchema.parse(request.query);
      const result = await this.projectService.getSuppliers(queryParams);

      return reply.send({
        success: true,
        data: result,
        message: '获取供应商列表成功'
      });
    } catch (error) {
      console.error('获取供应商列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取供应商列表失败'
      });
    }
  }

  /**
   * 获取单个供应商
   */
  async getSupplier(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const supplier = await this.projectService.getSupplier(id);

      if (!supplier) {
        return reply.status(404).send({
          success: false,
          message: '供应商不存在'
        });
      }

      return reply.send({
        success: true,
        data: supplier,
        message: '获取供应商成功'
      });
    } catch (error) {
      console.error('获取供应商失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取供应商失败'
      });
    }
  }

  /**
   * 更新供应商
   */
  async updateSupplier(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const bodyData = updateSupplierSchema.parse(request.body);

      // 将 URL 参数中的 id 添加到供应商数据中
      const supplierData = { id, ...bodyData };

      // 获取当前用户ID
      const updatedBy = 'current-user'; // 实际应该从认证信息中获取

      const supplier = await this.projectService.updateSupplier(supplierData, updatedBy);

      return reply.send({
        success: true,
        data: supplier,
        message: '更新供应商成功'
      });
    } catch (error) {
      console.error('更新供应商失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '更新供应商失败'
      });
    }
  }

  /**
   * 删除供应商
   */
  async deleteSupplier(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      await this.projectService.deleteSupplier(id);

      return reply.send({
        success: true,
        message: '删除供应商成功'
      });
    } catch (error) {
      console.error('删除供应商失败:', error);
      return reply.status(500).send({
        success: false,
        message: '删除供应商失败'
      });
    }
  }

  /**
   * 获取供应商统计
   */
  async getSupplierStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getSupplierStats();

      return reply.send({
        success: true,
        data: stats,
        message: '获取供应商统计成功'
      });
    } catch (error) {
      console.error('获取供应商统计失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取供应商统计失败'
      });
    }
  }

  /**
   * 批量创建供应商
   */
  async createSuppliersBatch(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { suppliers } = request.body as { suppliers: Array<z.infer<typeof createSupplierSchema>> };

      // 获取当前用户ID
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user'; // 实际应该从认证信息中获取

      const results = await this.supplierService.createSuppliersBatch(suppliers, createdBy);

      return reply.send({
        success: true,
        data: results,
        message: '批量创建供应商成功'
      });
    } catch (error) {
      console.error('批量创建供应商失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '批量创建供应商失败'
      });
    }
  }

  /**
   * 导入供应商
   */
  async importSuppliers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const data = await request.file();
      if (!data) {
        return reply.status(400).send({
          success: false,
          message: '没有上传文件'
        });
      }


      // 获取当前用户ID
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user'; // 实际应该从认证信息中获取

      // csv 2 string
      const chunks: Buffer[] = [];
      for await (const chunk of data.file) {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
      }
      const fileContent = Buffer.concat(chunks).toString('utf-8');

      const results = await this.supplierService.importSuppliers(fileContent, createdBy);

      return reply.send({
        success: true,
        data: results,
        message: '导入供应商成功'
      });
    } catch (error) {
      console.error('导入供应商失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '导入供应商失败'
      });
    }
  }
}
