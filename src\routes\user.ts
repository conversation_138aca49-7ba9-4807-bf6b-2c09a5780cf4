import { FastifyInstance } from 'fastify';
import { UserController } from '../controllers/user.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';


  export async function userRoutes(fastify: FastifyInstance) {
  const userController = new UserController();

  // 同步指定用户信息
  fastify.post('/users/sync', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_UPDATE)],
    schema: {
      description: '同步指定用户信息',
      tags: ['User'],
      body: {
        type: 'object',
        required: ['userIds'],
        properties: {
          userIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: '用户ID列表'
          },
          force: {
            type: 'boolean',
            default: false,
            description: '是否强制同步'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                syncedUsers: { type: 'number' },
                failedUsers: { type: 'number' },
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      error: { type: 'string' }
                    }
                  }
                },
                totalUsers: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.syncUsers.bind(userController));

  // 同步所有用户信息
  fastify.post('/users/sync-all', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_UPDATE)],
    schema: {
      description: '同步所有用户信息',
      tags: ['User'],
      body: {
        type: 'object',
        properties: {
          force: {
            type: 'boolean',
            default: false,
            description: '是否强制同步'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                syncedUsers: { type: 'number' },
                failedUsers: { type: 'number' },
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      error: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.syncAllUsers.bind(userController));

  // 批量获取用户信息
  fastify.post('/users/batch', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '批量获取用户信息',
      tags: ['User'],
      body: {
        type: 'object',
        required: ['userIds'],
        properties: {
          userIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: '用户ID列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      name: { type: 'string' },
                      avatar: { type: 'string' },
                      department: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getUsers.bind(userController));

  // 获取单个用户信息
  fastify.get('/users/:userid', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '获取单个用户信息',
      tags: ['User'],
      params: {
        type: 'object',
        required: ['userid'],
        properties: {
          userid: { type: 'string', description: '用户ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                name: { type: 'string' },
                avatar: { type: 'string' },
                department: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getUser.bind(userController));

  // 获取本地用户列表（分页）
  fastify.get('/users', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '获取本地用户列表',
      tags: ['User'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页大小' },
          keyword: { type: 'string', description: '搜索关键字' },
          isActive: { type: 'string', description: '是否活跃' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      unionid: { type: 'string' },
                      name: { type: 'string' },
                      avatar: { type: 'string' },
                      stateCode: { type: 'string' },
                      managerUserid: { type: 'string' },
                      mobile: { type: 'string' },
                      hideMobile: { type: 'boolean' },
                      telephone: { type: 'string' },
                      jobNumber: { type: 'string' },
                      title: { type: 'string' },
                      email: { type: 'string' },
                      workPlace: { type: 'string' },
                      remark: { type: 'string' },
                      loginId: { type: 'string' },
                      exclusiveAccountType: { type: 'string' },
                      exclusiveAccount: { type: 'boolean' },
                      deptIdList: { type: 'array', items: { type: 'number' } },
                      extension: { type: 'string' },
                      hiredDate: { type: 'string' },
                      active: { type: 'boolean' },
                      realAuthed: { type: 'boolean' },
                      orgEmail: { type: 'string' },
                      orgEmailType: { type: 'string' },
                      senior: { type: 'boolean' },
                      admin: { type: 'boolean' },
                      boss: { type: 'boolean' },
                      isActive: { type: 'boolean' },
                      lastSyncAt: { type: 'string' },
                      roles: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            displayName: { type: 'string' },
                            description: { type: 'string' },
                            isSystem: { type: 'boolean' },
                            isActive: { type: 'boolean' },
                            assignedAt: { type: 'string' },
                            expiresAt: { type: 'string' }
                          }
                        }
                      }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getLocalUsers.bind(userController));

  // 批量获取本地用户
  fastify.post('/users/local/batch', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '批量获取本地用户',
      tags: ['User'],
      body: {
        type: 'object',
        required: ['userIds'],
        properties: {
          userIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: '用户ID列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      name: { type: 'string' },
                      avatar: { type: 'string' },
                      stateCode: { type: 'string' },
                      managerUserid: { type: 'string' },
                      mobile: { type: 'string' },
                      hideMobile: { type: 'boolean' },
                      telephone: { type: 'string' },
                      jobNumber: { type: 'string' },
                      title: { type: 'string' },
                      email: { type: 'string' },
                      workPlace: { type: 'string' },
                      remark: { type: 'string' },
                      loginId: { type: 'string' },
                      exclusiveAccountType: { type: 'string' },
                      exclusiveAccount: { type: 'boolean' },
                      deptIdList: { type: 'array', items: { type: 'number' } },
                      extension: { type: 'string' },
                      hiredDate: { type: 'string' },
                      active: { type: 'boolean' },
                      realAuthed: { type: 'boolean' },
                      orgEmail: { type: 'string' },
                      orgEmailType: { type: 'string' },
                      senior: { type: 'boolean' },
                      admin: { type: 'boolean' },
                      boss: { type: 'boolean' },
                      lastSyncAt: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getLocalUsersBatch.bind(userController));

  // 批量根据用户id获取用户名
  fastify.post('/users/names', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '批量根据用户id获取用户名',
      tags: ['User'],
      body: {
        type: 'object',
        required: ['userIds'],
        properties: {
          userIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: '用户ID列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      name: { type: 'string' },
                      roles: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            displayName: { type: 'string' },
                            description: { type: 'string' },
                            isSystem: { type: 'boolean' },
                            isActive: { type: 'boolean' },
                            assignedAt: { type: 'string' },
                            expiresAt: { type: 'string' }
                          }
                        }
                      }
                    }
                  }
                },
                total: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getUserNames.bind(userController));

  // 根据用户id获取用户名
  fastify.get('/users/:userid/name', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '根据用户id获取用户名',
      tags: ['User'],
      params: {
        type: 'object',
        required: ['userid'],
        properties: {
          userid: { type: 'string', description: '用户ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                name: { type: 'string' },
                roles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      description: { type: 'string' },
                      isSystem: { type: 'boolean' },
                      isActive: { type: 'boolean' },
                      assignedAt: { type: 'string' },
                      expiresAt: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getUserName.bind(userController));

  // 获取用户同步状态统计
  fastify.get('/users/sync/stats', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '获取用户同步状态统计',
      tags: ['User'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalUsers: { type: 'number' },
                activeUsers: { type: 'number' },
                inactiveUsers: { type: 'number' },
                recentSyncUsers: { type: 'number' },
                outdatedUsers: { type: 'number' },
                syncRate: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getUserSyncStats.bind(userController));


  // 分页获取本地用户
  fastify.get('/users/local', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_READ)],
    schema: {
      description: '分页获取本地用户',
      tags: ['User'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页大小' },
          keyword: { type: 'string', description: '搜索关键字' },
          isActive: { type: 'string', description: '是否活跃' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      unionid: { type: 'string' },
                      name: { type: 'string' },
                      avatar: { type: 'string' },
                      stateCode: { type: 'string' },
                      managerUserid: { type: 'string' },
                      mobile: { type: 'string' },
                      hideMobile: { type: 'boolean' },
                      telephone: { type: 'string' },
                      jobNumber: { type: 'string' },
                      title: { type: 'string' },
                      email: { type: 'string' },
                      workPlace: { type: 'string' },
                      remark: { type: 'string' },
                      loginId: { type: 'string' },
                      exclusiveAccountType: { type: 'string' },
                      exclusiveAccount: { type: 'boolean' },
                      deptIdList: { type: 'array', items: { type: 'number' } },
                      extension: { type: 'string' },
                      hiredDate: { type: 'string' },
                      active: { type: 'boolean' },
                      realAuthed: { type: 'boolean' },
                      orgEmail: { type: 'string' },
                      orgEmailType: { type: 'string' },
                      senior: { type: 'boolean' },
                      admin: { type: 'boolean' },
                      boss: { type: 'boolean' },
                      isActive: { type: 'boolean' },
                      lastSyncAt: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.getLocalUsers.bind(userController));
    
  fastify.get('/users/sync', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.USER_UPDATE)],
    schema: {
      description: '同步所有用户信息',
      tags: ['User'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                syncedUsers: { type: 'number' },
                failedUsers: { type: 'number' },
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      error: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, userController.syncAllUsers.bind(userController));
}
