# Excel批量导入项目功能测试指南

## 功能概述

已成功实现Excel批量导入项目功能，支持：
- 根据Excel数据自动创建项目
- 自动创建品牌（如果不存在）
- 自动创建收入记录（如果有已汇款金额）
- 数据验证和错误报告
- 试运行模式（仅验证不创建）

## API接口

### 1. 获取导入模板
```
GET /api/projects/import/template
```

**权限要求**: `project.create`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "headers": ["orderTime", "brandName", "executeProject", ...],
    "headerLabels": {
      "orderTime": "下单时间",
      "brandName": "品牌名称",
      ...
    },
    "example": {
      "orderTime": "2025年1月",
      "brandName": "华帝",
      "projectName": "华帝2501站外推广",
      ...
    },
    "enums": {
      "contractSigningStatus": ["无合同", "已签订", "签订中", "待定"],
      "contractType": ["年框", "季框", "单次", "PO单", "京任务"],
      ...
    }
  }
}
```

### 2. 批量导入项目
```
POST /api/projects/import
```

**权限要求**: `project.create`

**请求体**:
```json
{
  "data": [
    {
      "orderTime": "2025年1月",
      "brandName": "华帝",
      "executeProject": "总项目",
      "projectName": "华帝2501站外推广",
      "contractSigningStatus": "签订中",
      "contractType": "年框-合同",
      "planningBudget": "591.3万",
      "expectedPaymentMonth": "2025年5月",
      "paymentTermDays": "T+90",
      "period": "1-2月",
      "amountReceived": "59.9万",
      "unpaidAmount": "0.0万",
      "reimbursementStatus": "回款中",
      "cost": "52.9万",
      "estimatedInfluencerRebate": "5.7万",
      "intermediary": ""
    }
  ],
  "options": {
    "createMissingBrands": true,
    "defaultExecutorPM": "user123",
    "defaultContentMediaIds": ["user456"],
    "dryRun": false
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "totalRows": 1,
    "successCount": 1,
    "failureCount": 0,
    "errors": [],
    "createdProjects": [
      {
        "row": 1,
        "projectId": "proj_123",
        "projectName": "华帝2501站外推广",
        "revenueId": "rev_456"
      }
    ],
    "warnings": [
      {
        "row": 1,
        "message": "将自动创建品牌\"华帝\""
      }
    ]
  },
  "message": "导入完成：成功1个，失败0个"
}
```

## 数据字段映射

| Excel字段 | 中文名称 | 项目字段 | 说明 |
|-----------|----------|----------|------|
| orderTime | 下单时间 | period.startDate | 解析年月信息 |
| brandName | 品牌名称 | brand.name | 自动查找或创建品牌 |
| executeProject | 执行项目 | documentType | 映射到单据类型 |
| projectName | 项目名称 | projectName | 必填字段 |
| contractSigningStatus | 合同签署状况 | contractSigningStatus | 枚举映射 |
| contractType | 合同类型 | contractType | 枚举映射 |
| planningBudget | 规划预算 | budget.planningBudget | 支持"万"单位 |
| expectedPaymentMonth | 预计回款月份 | expectedPaymentMonth | 转换为YYYY-MM格式 |
| paymentTermDays | 账期 | paymentTermDays | 解析T+数字格式 |
| period | 周期 | period | 解析月份范围 |
| amountReceived | 已汇款 | 创建收入记录 | 大于0时自动创建 |
| reimbursementStatus | 回款状态 | 收入状态 | 枚举映射 |
| cost | 成本 | cost.otherCost | 支持"万"单位 |
| estimatedInfluencerRebate | 预估达人返点 | cost.estimatedInfluencerRebate | 支持"万"单位 |

## 枚举值映射

### 合同签署状态
- "无合同" → `no_contract`
- "已签订" → `signed`
- "签订中" → `signing`
- "待定" → `pending`

### 合同类型
- "年框" / "年框-合同" → `annual_frame`
- "季框" / "季框-合同" → `quarterly_frame`
- "单次" / "单次-合同" → `single`
- "PO单" → `po_order`
- "京任务" → `jing_task`

### 单据类型
- "总项目" → `project_initiation`
- "项目立项" → `project_initiation`
- "项目提案" → `project_proposal`
- "项目计划" → `project_plan`
- "项目执行" → `project_execution`
- "项目总结" → `project_summary`

### 收入状态
- "收款中" / "回款中" → `receiving`
- "已收款" / "已回款" → `received`
- "已取消" → `cancelled`

## 测试步骤

### 1. 准备测试数据
使用您提供的示例数据：
```javascript
const testData = [
  {
    orderTime: "2025年1月",
    brandName: "华帝",
    executeProject: "总项目",
    projectName: "华帝2501站外推广",
    contractSigningStatus: "签订中",
    contractType: "年框-合同",
    planningBudget: "591.3万",
    expectedPaymentMonth: "2025年5月",
    paymentTermDays: "T+90",
    period: "1-2月",
    amountReceived: "59.9万",
    unpaidAmount: "0.0万",
    reimbursementStatus: "回款中",
    cost: "52.9万",
    estimatedInfluencerRebate: "5.7万",
    intermediary: ""
  }
];
```

### 2. 试运行测试
```bash
curl -X POST http://localhost:3000/api/projects/import \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "data": [测试数据],
    "options": {
      "createMissingBrands": true,
      "dryRun": true
    }
  }'
```

### 3. 实际导入测试
```bash
curl -X POST http://localhost:3000/api/projects/import \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "data": [测试数据],
    "options": {
      "createMissingBrands": true,
      "defaultExecutorPM": "your-user-id",
      "defaultContentMediaIds": ["content-user-id"],
      "dryRun": false
    }
  }'
```

### 4. 验证结果
- 检查项目是否创建成功
- 检查品牌是否自动创建
- 检查收入记录是否创建（59.9万的已汇款）
- 检查项目数据是否正确映射

## 错误处理

系统会验证以下内容：
- 必填字段（项目名称、品牌名称、规划预算）
- 金额格式（支持"万"单位）
- 枚举值有效性
- 品牌存在性（可选择自动创建）
- 数据类型正确性

## 注意事项

1. **权限要求**: 需要 `project.create` 权限
2. **品牌创建**: 启用 `createMissingBrands` 选项可自动创建不存在的品牌
3. **默认值**: 建议设置 `defaultExecutorPM` 和 `defaultContentMediaIds`
4. **金额单位**: 支持"万"单位，会自动转换为元
5. **收入创建**: 只有当 `amountReceived` 大于0时才会创建收入记录
6. **试运行**: 使用 `dryRun: true` 可以先验证数据而不实际创建

## 成功标准

导入成功后应该：
- 项目创建成功，包含正确的预算、成本、利润信息
- 品牌自动创建（如果不存在）
- 收入记录创建成功，状态为"收款中"，金额为59.9万
- 项目周期正确解析为2025年1-2月
- 预计回款月份设置为2025-05
- 账期设置为90天
