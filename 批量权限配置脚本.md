# 批量权限配置脚本

## 剩余需要配置的API权限

### 1. 收入管理API (src/routes/revenue.ts)

需要进行以下替换：

```typescript
// 查看收入API
preHandler: [jwtAuthMiddleware] → preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_READ)]

// 更新收入API  
preHandler: [jwtAuthMiddleware] → preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_UPDATE)]

// 删除收入API
preHandler: [jwtAuthMiddleware] → preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_DELETE)]

// 确认收入API (审批操作)
preHandler: [jwtAuthMiddleware] → preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.FINANCE_APPROVE)]
```

具体替换位置：
- 第54行：`fastify.get('/revenues', {` → 添加 FINANCE_READ
- 第139行：`fastify.get('/revenues/:id', {` → 添加 FINANCE_READ  
- 第165行：`fastify.put('/revenues/:id', {` → 添加 FINANCE_UPDATE
- 第218行：`fastify.delete('/revenues/:id', {` → 添加 FINANCE_DELETE
- 第243行：`fastify.put('/revenues/:id/confirm', {` → 添加 FINANCE_APPROVE
- 第278行：`fastify.put('/revenues/batch-confirm', {` → 添加 FINANCE_APPROVE
- 第337行：`fastify.get('/revenues/stats', {` → 添加 FINANCE_READ

### 2. 审批管理API (src/routes/approval.ts)

需要添加导入并配置权限：

```typescript
// 添加导入
import { requirePermission, PERMISSIONS } from '../middleware/permission.js';

// 权限配置
- 查看审批 → PERMISSIONS.FINANCE_READ
- 创建审批 → PERMISSIONS.FINANCE_CREATE  
- 审批操作 → PERMISSIONS.FINANCE_APPROVE
```

### 3. 项目管理API剩余部分 (src/routes/project.ts)

需要为剩余的项目API配置权限：

```typescript
// 查看项目相关API → PERMISSIONS.PROJECT_READ
// 创建项目API → PERMISSIONS.PROJECT_CREATE
// 更新项目API → PERMISSIONS.PROJECT_UPDATE
// 品牌查看API → PERMISSIONS.BRAND_READ
// 品牌更新API → PERMISSIONS.BRAND_UPDATE
// 品牌删除API → PERMISSIONS.BRAND_DELETE
```

### 4. 财务管理API剩余部分 (src/routes/financial.ts)

继续配置剩余的财务API权限。

### 5. 系统管理API

如果有系统配置、日志等API，需要配置：
- PERMISSIONS.SYSTEM_CONFIG
- PERMISSIONS.SYSTEM_LOG  
- PERMISSIONS.SYSTEM_BACKUP

## 快速批量替换命令

可以使用以下模式进行批量替换：

### 模式1：查看权限
```
查找：preHandler: \[jwtAuthMiddleware\],
替换为：preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.XXX_READ)],
```

### 模式2：创建权限
```
查找：preHandler: \[jwtAuthMiddleware\],
替换为：preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.XXX_CREATE)],
```

### 模式3：更新权限
```
查找：preHandler: \[jwtAuthMiddleware\],
替换为：preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.XXX_UPDATE)],
```

### 模式4：删除权限
```
查找：preHandler: \[jwtAuthMiddleware\],
替换为：preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.XXX_DELETE)],
```

## 权限映射表

| API类型 | 权限常量 | 说明 |
|---------|----------|------|
| GET /revenues | FINANCE_READ | 查看收入 |
| POST /revenues | FINANCE_CREATE | 创建收入 |
| PUT /revenues | FINANCE_UPDATE | 更新收入 |
| DELETE /revenues | FINANCE_DELETE | 删除收入 |
| PUT /revenues/confirm | FINANCE_APPROVE | 确认收入 |
| GET /projects | PROJECT_READ | 查看项目 |
| POST /projects | PROJECT_CREATE | 创建项目 |
| PUT /projects | PROJECT_UPDATE | 更新项目 |
| DELETE /projects | PROJECT_DELETE | 删除项目 |
| GET /brands | BRAND_READ | 查看品牌 |
| POST /brands | BRAND_CREATE | 创建品牌 |
| PUT /brands | BRAND_UPDATE | 更新品牌 |
| DELETE /brands | BRAND_DELETE | 删除品牌 |
| GET /suppliers | SUPPLIER_READ | 查看供应商 |
| POST /suppliers | SUPPLIER_CREATE | 创建供应商 |
| PUT /suppliers | SUPPLIER_UPDATE | 更新供应商 |
| DELETE /suppliers | SUPPLIER_DELETE | 删除供应商 |
| GET /weekly-budgets | BUDGET_READ | 查看预算 |
| POST /weekly-budgets | BUDGET_CREATE | 创建预算 |
| PUT /weekly-budgets | BUDGET_UPDATE | 更新预算 |
| DELETE /weekly-budgets | BUDGET_DELETE | 删除预算 |
| POST /weekly-budgets/approval | BUDGET_APPROVE | 审批预算 |
| GET /departments | DEPARTMENT_READ | 查看部门 |
| POST /departments | DEPARTMENT_CREATE | 创建部门 |
| PUT /departments | DEPARTMENT_UPDATE | 更新部门 |
| DELETE /departments | DEPARTMENT_DELETE | 删除部门 |
| GET /financial/export | REPORT_EXPORT | 导出报表 |
| GET /users | USER_READ | 查看用户 |
| POST /users | USER_CREATE | 创建用户 |
| PUT /users | USER_UPDATE | 更新用户 |
| DELETE /users | USER_DELETE | 删除用户 |
| GET /roles | ROLE_READ | 查看角色 |
| POST /roles | ROLE_CREATE | 创建角色 |
| PUT /roles | ROLE_UPDATE | 更新角色 |
| DELETE /roles | ROLE_DELETE | 删除角色 |
| POST /roles/assign | ROLE_ASSIGN | 分配角色 |
| GET /permissions | PERMISSION_READ | 查看权限 |
| POST /permissions | PERMISSION_CREATE | 创建权限 |
| PUT /permissions | PERMISSION_UPDATE | 更新权限 |
| DELETE /permissions | PERMISSION_DELETE | 删除权限 |

## 验证清单

配置完成后需要验证：

1. ✅ 所有路由文件都已导入权限中间件
2. ✅ 所有API都配置了相应的权限验证
3. ✅ 权限常量已完整定义
4. ✅ 数据库中包含所有权限记录
5. ✅ 角色已分配相应权限
6. ✅ 测试权限验证功能正常

## 下一步

1. 完成剩余API的权限配置
2. 运行权限初始化脚本确保数据库包含所有权限
3. 测试不同角色的用户访问权限
4. 编写权限配置的文档和测试用例
