import { FastifyInstance } from 'fastify';
import { ApprovalController } from '../controllers/approval.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { FileUploadHelper } from '../utils/fileUpload.js';

export async function approvalRoutes(fastify: FastifyInstance) {
  const approvalController = new ApprovalController();

  // 审批管理路由

  // 发起对公付款审批
  fastify.post('/approvals/payment', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '发起对公付款审批',
      tags: ['Approval'],
      body: {
        type: 'object',
        required: ['weeklyBudgetId', 'approvalAmount', 'department'],
        properties: {
          weeklyBudgetId: { type: 'string', description: '周预算ID' },
          approvalAmount: { type: 'number', minimum: 0.01, description: '审批金额' },
          reason: { type: 'string', description: '审批原因' },
          remark: { type: 'string', description: '备注' },
          relatedApprovalId: { type: 'string', description: '关联审批单' },
          invoiceFiles: {
            // type: 'array',
            type: 'array',
            items: {
              type: 'object',
              properties: {
                fileId: { type: 'string' },
                fileName: { type: 'string' },
                fileSize: { type: 'string' },
                fileType: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' }
              }
            },
            description: '发票文件URL列表'
          },
          attachments: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                fileId: { type: 'string' },
                fileName: { type: 'string' },
                fileSize: { type: 'string' },
                fileType: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' }
              }
            },
            description: '附件URL列表'
          },
          contractEntity: {
            type: 'string',
            enum: ['company_a', 'company_b', 'subsidiary', 'other'],
            description: '合同签署主体'
          },
          paymentMethod: {
            type: 'string',
            enum: ['bank_transfer', 'online_payment', 'check', 'cash', 'other'],
            description: '付款方式'
          },
          receivingAccount: {
            type: 'object',
            required: ['accountName', 'accountNumber', 'bankName'],
            properties: {
              accountName: { type: 'string', description: '账户名称' },
              accountNumber: { type: 'string', description: '账号' },
              bankName: { type: 'string', description: '开户银行' },
              bankCode: { type: 'string', description: '银行代码' }
            }
          },
          expectedPaymentDate: { type: 'string', format: 'date', description: '期望付款时间' },
          // 兼容旧版本字段
          totalAmount: { type: 'number', minimum: 0.01, description: '付款总额（兼容）' },
          paymentReason: { type: 'string', description: '付款事由（兼容）' },
          department: { type: 'number', description: '申请部门（兼容）' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.createPaymentApproval.bind(approvalController));

  // 发起带附件的对公付款审批
  fastify.post('/approvals/payment-with-files', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '发起带附件的对公付款审批',
      tags: ['Approval'],
      consumes: ['multipart/form-data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                processInstanceId: { type: 'string' },
                approvalId: { type: 'string' },
                uploadedFiles: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.createPaymentApprovalWithFiles.bind(approvalController));

  // 获取审批实例列表
  fastify.get('/approvals', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取审批实例列表',
      tags: ['Approval'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          weeklyBudgetId: { type: 'string', description: '周预算ID' },
          status: {
            type: 'string',
            enum: ['NONE', 'PENDING', 'APPROVED', 'REJECTED', 'CANCELLED'],
            description: '审批状态'
          },
          originatorUserId: { type: 'string', description: '发起人用户ID' },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          sortBy: { type: 'string', description: '排序字段' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], description: '排序方向' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                approvals: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      processInstanceId: { type: 'string' },
                      title: { type: 'string' },
                      status: { type: 'string' },
                      approvalAmount: { type: 'number' },
                      createTime: { type: 'string' },
                      weeklyBudget: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          title: { type: 'string' }
                        }
                      }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.getApprovalInstances.bind(approvalController));

  // 获取单个审批实例
  fastify.get('/approvals/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取单个审批实例',
      tags: ['Approval'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '审批实例ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                processInstanceId: { type: 'string' },
                processCode: { type: 'string' },
                businessId: { type: 'string' },
                title: { type: 'string' },
                originatorUserId: { type: 'string' },
                status: { type: 'string' },
                result: { type: 'string' },
                createTime: { type: 'string' },
                finishTime: { type: 'string' },
                approvalAmount: { type: 'number' },
                actualAmount: { type: 'number' },
                reason: { type: 'string' },
                remark: { type: 'string' },
                weeklyBudget: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    title: { type: 'string' },
                    project: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        name: { type: 'string' }
                      }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.getApprovalInstance.bind(approvalController));

  // 同步审批状态
  fastify.get('/approvals/sync/:processInstanceId', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '同步审批状态',
      tags: ['Approval'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                status: { type: 'string' },
                result: { type: 'string' },
                finishTime: { type: 'string' },
                actualAmount: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.syncApprovalStatus.bind(approvalController));

  // 获取审批统计信息
  fastify.get('/approvals/stats', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取审批统计信息',
      tags: ['Approval'],
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                pending: { type: 'number' },
                approved: { type: 'number' },
                rejected: { type: 'number' },
                cancelled: { type: 'number' },
                totalAmount: { type: 'number' },
                approvedAmount: { type: 'number' },
                pendingAmount: { type: 'number' }
              }
              },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.getApprovalStats.bind(approvalController));

  // 批量同步审批状态
  fastify.post('/approvals/batch-sync', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '批量同步审批状态',
      tags: ['Approval'],
      body: {
        type: 'object',
        required: ['processInstanceIds'],
        properties: {
          processInstanceIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: '钉钉审批实例ID列表'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                success: { type: 'number' },
                failure: { type: 'number' },
                results: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      processInstanceId: { type: 'string' },
                      status: { type: 'string' },
                      data: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          status: { type: 'string' },
                          result: { type: 'string' },
                          finishTime: { type: 'string' },
                          actualAmount: { type: 'number' }
                        }
                      },
                      error: { type: 'string' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.batchSyncApprovalStatus.bind(approvalController));

  // 审批回调接口（钉钉回调）
  fastify.post('/approvals/callback', {
    schema: {
      description: '审批回调接口',
      tags: ['Approval'],
      body: {
        type: 'object',
        description: '钉钉审批回调数据'
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, approvalController.handleApprovalCallback.bind(approvalController));

  // 审批文件上传接口
  fastify.post('/approvals/upload/:type', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '上传审批相关文件',
      tags: ['Approval'],
      params: {
        type: 'object',
        required: ['type'],
        properties: {
          type: {
            type: 'string',
            enum: ['invoice', 'attachment'],
            description: '文件类型：invoice-发票文件，attachment-附件'
          }
        }
      },
      consumes: ['multipart/form-data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                files: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      filename: { type: 'string' },
                      originalName: { type: 'string' },
                      mediaId: { type: 'string' },
                      size: { type: 'number' },
                      mimeType: { type: 'string' }
                    }
                  }
                },
                mediaIds: {
                  type: 'array',
                  items: { type: 'string' }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { type } = request.params as { type: 'invoice' | 'attachment' };
      const user = (request as any).user;

      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证'
        });
      }

      const files = request.files();
      const uploadedFiles: any[] = [];
      const fileList: any[] = [];

      // 收集所有文件
      for await (const file of files) {
        // 验证文件
        const validation = type === 'invoice'
          ? FileUploadHelper.validateInvoiceFile(file.filename, file.file.bytesRead || 0)
          : FileUploadHelper.validateAttachmentFile(file.filename, file.file.bytesRead || 0);

        if (!validation.valid) {
          return reply.status(400).send({
            success: false,
            message: validation.error
          });
        }

        fileList.push(file);
      }

      if (fileList.length === 0) {
        return reply.status(400).send({
          success: false,
          message: '没有上传文件'
        });
      }

      // 上传文件到钉钉媒体库
      const { DingTalkService } = await import('../services/dingtalk.js');
      const dingTalkService = new DingTalkService();
      const mediaIds = await dingTalkService.uploadAttachmentFiles(fileList);

      // 构建返回数据
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        const mediaId = mediaIds[i];

        uploadedFiles.push({
          filename: file.filename,
          originalName: file.filename,
          mediaId: mediaId,
          size: file.file.bytesRead || 0,
          mimeType: file.mimetype
        });
      }

      return reply.send({
        success: true,
        data: {
          files: uploadedFiles,
          mediaIds: mediaIds
        },
        message: `${type === 'invoice' ? '发票' : '附件'}文件上传到钉钉成功`
      });
    } catch (error) {
      console.error('文件上传到钉钉失败:', error);
      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '文件上传失败'
      });
    }
  });

  console.log('🔐 审批管理路由已注册');
}
