// Excel导入功能测试脚本
// 使用方法: node test-import.js

const testData = [
  {
    orderTime: "2025年1月",
    brandName: "华帝",
    executeProject: "总项目",
    projectName: "华帝2501站外推广",
    contractSigningStatus: "签订中",
    contractType: "年框-合同",
    planningBudget: "591.3万",
    expectedPaymentMonth: "2025年5月",
    paymentTermDays: "T+90",
    period: "1-2月",
    amountReceived: "59.9万",
    unpaidAmount: "0.0万",
    reimbursementStatus: "回款中",
    cost: "52.9万",
    estimatedInfluencerRebate: "5.7万",
    intermediary: ""
  }
];

async function testImport() {
  const baseUrl = 'http://localhost:3000/api';
  
  // 请替换为您的JWT token
  const token = 'YOUR_JWT_TOKEN_HERE';
  
  if (token === 'YOUR_JWT_TOKEN_HERE') {
    console.log('❌ 请先设置有效的JWT token');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  try {
    console.log('🔍 1. 获取导入模板...');
    const templateResponse = await fetch(`${baseUrl}/projects/import/template`, {
      method: 'GET',
      headers
    });
    
    if (!templateResponse.ok) {
      throw new Error(`获取模板失败: ${templateResponse.status}`);
    }
    
    const templateData = await templateResponse.json();
    console.log('✅ 模板获取成功');
    console.log('📋 支持的字段:', templateData.data.headers);
    console.log('📝 示例数据:', templateData.data.example);

    console.log('\n🧪 2. 试运行导入（仅验证）...');
    const dryRunResponse = await fetch(`${baseUrl}/projects/import`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        data: testData,
        options: {
          createMissingBrands: true,
          dryRun: true
        }
      })
    });

    if (!dryRunResponse.ok) {
      throw new Error(`试运行失败: ${dryRunResponse.status}`);
    }

    const dryRunResult = await dryRunResponse.json();
    console.log('✅ 试运行完成');
    console.log('📊 验证结果:', {
      总行数: dryRunResult.data.totalRows,
      有效行数: dryRunResult.data.successCount,
      错误行数: dryRunResult.data.failureCount
    });

    if (dryRunResult.data.errors.length > 0) {
      console.log('❌ 发现错误:');
      dryRunResult.data.errors.forEach(error => {
        console.log(`  第${error.row}行: ${error.message}`);
      });
      return;
    }

    if (dryRunResult.data.warnings.length > 0) {
      console.log('⚠️ 警告信息:');
      dryRunResult.data.warnings.forEach(warning => {
        console.log(`  第${warning.row}行: ${warning.message}`);
      });
    }

    console.log('\n🚀 3. 执行实际导入...');
    const importResponse = await fetch(`${baseUrl}/projects/import`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        data: testData,
        options: {
          createMissingBrands: true,
          defaultExecutorPM: 'your-user-id', // 请替换为实际的用户ID
          defaultContentMediaIds: ['content-user-id'], // 请替换为实际的用户ID
          dryRun: false
        }
      })
    });

    if (!importResponse.ok) {
      throw new Error(`导入失败: ${importResponse.status}`);
    }

    const importResult = await importResponse.json();
    console.log('✅ 导入完成');
    console.log('📊 导入结果:', {
      总行数: importResult.data.totalRows,
      成功数: importResult.data.successCount,
      失败数: importResult.data.failureCount
    });

    if (importResult.data.createdProjects.length > 0) {
      console.log('🎉 创建的项目:');
      importResult.data.createdProjects.forEach(project => {
        console.log(`  第${project.row}行: ${project.projectName} (ID: ${project.projectId})`);
        if (project.revenueId) {
          console.log(`    💰 创建收入记录: ${project.revenueId}`);
        }
      });
    }

    if (importResult.data.errors.length > 0) {
      console.log('❌ 导入错误:');
      importResult.data.errors.forEach(error => {
        console.log(`  第${error.row}行: ${error.message}`);
      });
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
  // Node.js环境需要安装node-fetch: npm install node-fetch
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    testImport();
  } catch (e) {
    console.log('请安装node-fetch: npm install node-fetch');
    console.log('或者在浏览器控制台中运行此脚本');
  }
} else {
  // 浏览器环境
  testImport();
}

// 浏览器测试版本（复制到浏览器控制台）
console.log(`
// 浏览器测试版本 - 复制以下代码到浏览器控制台运行:

const testData = ${JSON.stringify(testData, null, 2)};

async function testImportInBrowser() {
  const token = prompt('请输入JWT token:');
  if (!token) return;
  
  const headers = {
    'Authorization': \`Bearer \${token}\`,
    'Content-Type': 'application/json'
  };
  
  try {
    // 试运行
    const dryRunResponse = await fetch('/api/projects/import', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        data: testData,
        options: { createMissingBrands: true, dryRun: true }
      })
    });
    
    const dryRunResult = await dryRunResponse.json();
    console.log('试运行结果:', dryRunResult);
    
    if (dryRunResult.data.errors.length === 0 && confirm('试运行成功，是否执行实际导入？')) {
      // 实际导入
      const importResponse = await fetch('/api/projects/import', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          data: testData,
          options: {
            createMissingBrands: true,
            defaultExecutorPM: 'your-user-id',
            defaultContentMediaIds: ['content-user-id'],
            dryRun: false
          }
        })
      });
      
      const importResult = await importResponse.json();
      console.log('导入结果:', importResult);
    }
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// testImportInBrowser();
`);
