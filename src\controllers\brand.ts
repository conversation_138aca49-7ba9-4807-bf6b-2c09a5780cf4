import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { BrandService } from '../services/brand.js';
import { ProjectService } from '../services/project.js';


const createBrandSchema = z.object({
  name: z.string().min(1, '品牌名称不能为空'),
  status: z.enum(['active', 'inactive']).optional(),
  description: z.string().optional(),
  logo: z.string().optional()
});

const updateBrandSchema = createBrandSchema.partial().extend({
  id: z.string().min(1, '品牌ID不能为空'),
  status: z.enum(['active', 'inactive']).optional()
});

const brandQuerySchema = z.object({
  page: z.string().optional().transform((str, ctx) => {
    if (!str) return undefined;
    const num = parseInt(str);
    if (isNaN(num) || num < 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '页码必须是大于0的整数'
      });
      return z.NEVER;
    }
    return num;
  }),
  pageSize: z.string().optional().transform((str, ctx) => {
    if (!str) return undefined;
    const num = parseInt(str);
    if (isNaN(num) || num < 1 || num > 100) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '每页数量必须是1-100之间的整数'
      });
      return z.NEVER;
    }
    return num;
  }),
  status: z.enum(['active', 'inactive']).optional(),
  keyword: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

const brandParamsSchema = z.object({
  id: z.string().min(1, '品牌ID不能为空')
});

export class BrandController {
  private projectService: ProjectService;
  private brandService: BrandService;

  constructor() {
    this.projectService = new ProjectService();
    this.brandService = new BrandService();
  }

  
  /**
   * 创建品牌
   */
  async createBrand(request: FastifyRequest, reply: FastifyReply) {
    try {
      const brandData = createBrandSchema.parse(request.body);

      // 获取当前用户ID
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      const brand = await this.projectService.createBrand(brandData, createdBy);

      return reply.send({
        success: true,
        data: brand,
        message: '品牌创建成功'
      });
    } catch (error) {

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      // 判断失败信息
      if (error instanceof Error) {
        if (error.message.includes('Unique constraint failed on the fields: (`name`)')) {
          return reply.status(400).send({
            success: false,
            message: '品牌名称已存在'
          });
        }
      }

      return reply.status(500).send({
        success: false,
        message: '创建品牌失败'
      });
    }
  }

  /**
   * 获取品牌列表
   */
  async getBrands(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = brandQuerySchema.parse(request.query);
      console.log('获取品牌列表:', queryParams);
      const result = await this.projectService.getBrands(queryParams);
      console.log('获取品牌列表成功:', result);

      const res = reply.send({
        success: true,
        data: result,
        message: '获取品牌列表成功'
      });
      console.log('发送响应:', res);
      return res
    } catch (error) {
      console.error('获取品牌列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取品牌列表失败'
      });
    }
  }

  /**
   * 获取单个品牌
   */
  async getBrand(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      if (!id) {
        return reply.status(400).send({
          success: false,
          message: '品牌ID不能为空'
        });
      }

      const brand = await this.projectService.getBrand(id);

      if (!brand) {
        return reply.status(404).send({
          success: false,
          message: '品牌不存在'
        });
      }

      return reply.send({
        success: true,
        data: brand,
        message: '获取品牌成功'
      });
    } catch (error) {
      console.error('获取品牌失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取品牌失败'
      });
    }
  }

  /**
   * 更新品牌
   */
  async updateBrand(request: FastifyRequest, reply: FastifyReply) {
    try {
      const brandData = updateBrandSchema.parse(request.body);
      const brand = await this.projectService.updateBrand(brandData);

      return reply.send({
        success: true,
        data: brand,
        message: '品牌更新成功'
      });
    } catch (error) {
      console.error('更新品牌失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '更新品牌失败'
      });
    }
  }

  /**
   * 删除品牌
   */
  async deleteBrand(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      if (!id) {
        return reply.status(400).send({
          success: false,
          message: '品牌ID不能为空'
        });
      }

      const success = await this.projectService.deleteBrand(id);

      if (!success) {
        return reply.status(404).send({
          success: false,
          message: '品牌不存在'
        });
      }

      return reply.send({
        success: true,
        message: '品牌删除成功'
      });
    } catch (error) {
      console.error('删除品牌失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '删除品牌失败'
      });
    }
  }

  /**
   * 批量创建品牌
   */
  async createBrandsBatch(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { brands } = request.body as { brands: Array<{ name: string; description?: string; logo?: string; status?: 'active' | 'inactive' }> };

      // 获取当前用户ID
      const createdBy = 'current-user'; // 实际应该从认证信息中获取

      const results = await this.brandService.createBrandsBatch(brands, createdBy);

      return reply.send({
        success: true,
        data: results,
        message: '批量创建品牌成功'
      });
    } catch (error) {
      console.error('批量创建品牌失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '批量创建品牌失败'
      });
    }
  }

}